# Kiến trúc ứng dụng Gen Voice

## Tổng quan

Gen Voice là một ứng dụng desktop được xây dựng bằng Electron, Vue 3 và TypeScript, cho phép người dùng tạo và quản lý giọng nói AI. Ứng dụng sử dụng kiến trúc đa tiến trình (multi-process) của Electron với các thành phần chính:

- Main Process: Quản lý ứng dụng, xử lý IPC và tương tác với hệ thống
- Renderer Process: Giao diện người dùng được xây dựng bằng Vue 3
- Preload Script: Cầu nối an toàn giữa Main và Renderer Process
- Python Process: Xử lý các tác vụ AI và xử lý âm thanh

## Cấu trúc thư mục
  
```
src/
├── main/                 # Main Process
│   ├── index.ts         # Điểm khởi đầu của Main Process
│   ├── logger/          # Hệ thống logging
│   ├── service/         # Các service chính
│   │   ├── FinetuneService.ts    # Xử lý fine-tuning
│   │   ├── PythonService.ts      # Quản lý Python process
│   │   ├── SpeechService.ts      # Xử lý text-to-speech
│   │   ├── TTSQueue.ts           # Quản lý hàng đợi TTS
│   │   └── WindowManager.ts      # Quản lý cửa sổ ứng dụng
│   └── store/           # Quản lý dữ liệu
│       ├── TTSProcessStore.ts    # Lưu trữ trạng thái TTS
│       └── VoiceStore.ts         # Quản lý voice models
│
├── preload/             # Preload Script
│   ├── index.ts        # Điểm khởi đầu của Preload
│   └── logger.ts       # Logger cho Preload
│
├── renderer/            # Renderer Process (Vue 3)
│   ├── assets/         # Tài nguyên tĩnh
│   ├── components/     # Vue components
│   │   ├── common/     # Components dùng chung
│   │   ├── finetune/   # Components cho fine-tuning
│   │   ├── settings/   # Components cho cài đặt
│   │   └── voices/     # Components quản lý voice
│   ├── composables/    # Vue composables
│   ├── locales/        # Đa ngôn ngữ (i18n)
│   ├── utils/          # Tiện ích
│   ├── views/          # Các trang chính
│   ├── App.vue         # Component gốc
│   ├── i18n.ts         # Cấu hình i18n
│   ├── main.ts         # Điểm khởi đầu của Renderer
│   └── router.ts       # Vue Router
│
├── types/              # TypeScript type definitions
│   └── index.ts        # Types chung
│
└── python/             # Python scripts
    ├── finetune.py     # Script fine-tuning
    ├── metadata_processor.py  # Xử lý metadata
    └── tts.py          # Text-to-speech
```

## Luồng dữ liệu

1. **Text-to-Speech (TTS)**
   - Renderer gửi yêu cầu TTS qua IPC
   - Main Process thêm task vào TTSQueue
   - Python process xử lý và trả kết quả
   - Kết quả được gửi lại Renderer qua IPC

2. **Fine-tuning**
   - Renderer gửi yêu cầu fine-tuning
   - Main Process khởi tạo Python process
   - Python process thực hiện fine-tuning
   - Tiến trình được cập nhật real-time qua IPC

3. **Voice Management**
   - VoiceStore quản lý dữ liệu voice
   - Import/Export voice qua IPC
   - Lưu trữ trong thư mục userData

## IPC Communication

### Main Process -> Renderer
- `processStatusUpdate`: Cập nhật trạng thái process
- `metadataProgress`: Tiến trình xử lý metadata
- `metadataComplete`: Hoàn thành xử lý metadata
- `metadata-error`: Lỗi xử lý metadata
- `toast`: Hiển thị thông báo

### Renderer -> Main Process
- Voice operations: `getVoices`, `addVoice`, `updateVoice`, `removeVoice`
- TTS operations: `textToSpeech`, `cancelProcess`
- Fine-tuning: `startFinetune`, `cancelFinetune`, `getFinetuneStatus`
- File operations: `selectFolder`, `selectFiles`, `selectVoiceZip`
- Settings: `getStoreValue`, `setStoreValue`

## Data Storage

- **Electron Store**: Lưu cài đặt ứng dụng
- **File System**: 
  - `userData/voices`: Thư mục chứa voice models
  - `userData/temp`: Thư mục tạm
  - `userData/logs`: Log files

## Dependencies

### Main Process
- electron-store: Lưu trữ dữ liệu
- winston: Logging
- uuid: Tạo ID

### Renderer Process
- Vue 3: Framework UI
- Vue Router: Điều hướng
- Vue I18n: Đa ngôn ngữ
- TailwindCSS: Styling

### Python
- TTS: Text-to-speech
- torch: Deep learning
- transformers: NLP
- ffmpeg-python: Xử lý audio