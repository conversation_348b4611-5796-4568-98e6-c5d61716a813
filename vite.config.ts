import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import vueI18n from '@intlify/unplugin-vue-i18n/vite';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';

  return {
  plugins: [
    vue(),
    vueI18n({
      include: resolve(__dirname, './src/renderer/locales/**'),
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@renderer': resolve(__dirname, 'src/renderer'),
      '@main': resolve(__dirname, 'src/main'),
      '@preload': resolve(__dirname, 'src/preload'),
      '@python': resolve(__dirname, 'src/python'),
      '@store': resolve(__dirname, 'src/store'),
      '@types': resolve(__dirname, 'src/types'),
      '@utils': resolve(__dirname, 'src/utils'),
    },
  },
  build: {
    outDir: 'dist',
    emptyOutDir: false, // Không xóa các file đã build bởi esbuild
    assetsDir: 'assets',  // Đặt assets vào thư mục assets
    assetsInlineLimit: 0, // Không inline bất kỳ tài nguyên nào
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
      external: [],
    },
  },
  base: './', // Sử dụng đường dẫn tương đối cho các file tài nguyên
  server: {
    port: 11001,
    strictPort: true, // Không tự động chuyển sang cổng khác nếu cổng 11001 đã được sử dụng
  },
  // Truyền các biến môi trường vào ứng dụng
  define: {
    'process.env': {
      ...env, // Truyền tất cả các biến môi trường
      PYTORCH_ENABLE_MPS_FALLBACK: env.VITE_PYTORCH_ENABLE_MPS_FALLBACK || '1', // Đảm bảo biến này luôn có giá trị mặc định
    },
  },
  };
})
