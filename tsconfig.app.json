{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@renderer/*": ["src/renderer/*"],
      "@main/*": ["src/main/*"],
      "@preload/*": ["src/preload/*"],
      "@python/*": ["src/python/*"],
      "@store/*": ["src/store/*"],
      "@types": ["src/types"],
      "@utils/*": ["src/utils/*"]
    },

    /* Linting */
    "strict": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
    "typeRoots": [
      "./src/types",
      "./node_modules/@types"
    ],
    "skipLibCheck": true,
    "verbatimModuleSyntax": false,
    "noImplicitAny": false,
    "resolveJsonModule": true,
    "esModuleInterop": true,
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ]
}
