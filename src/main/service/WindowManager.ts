import {BrowserWindow, app} from 'electron';
import path from 'path';
import loggerCore from '../logger/core';
import logger from '../logger/client';
import {screen} from 'electron';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private static instance: WindowManager;

  private constructor() {
  }

  public static getInstance(): WindowManager {
    if (!WindowManager.instance) {
      WindowManager.instance = new WindowManager();
    }
    return WindowManager.instance;
  }

  public createWindow() {
    const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
    logger.info('Creating window...', { VITE_DEV_SERVER_URL }, 'system');

    // L<PERSON>y đường dẫn đến thư mục gốc của ứng dụng
    const appPath = app.getAppPath();
    const preloadPath = path.join(appPath, 'dist/preload.js');
    logger.info('App paths:', { appPath, preloadPath }, 'system');

    try {
      const { width, height } = screen.getPrimaryDisplay().workAreaSize;
      this.mainWindow = new BrowserWindow({
        width: width,
        height: height,
        icon: path.join(app.getAppPath(), 'public', process.platform === 'darwin' ? 'logo.icns' : 'logo.ico'),
        webPreferences: {
          preload: preloadPath,
          nodeIntegration: false,
          contextIsolation: true
        },
      });
    } catch (err) {
      logger.error('Failed to create BrowserWindow:', { error: err.message }, 'system');
      throw err;
    }

    logger.info('Preload path:', { preloadPath }, 'system');

    // Tải URL từ dev server hoặc file HTML local
    if (VITE_DEV_SERVER_URL) {
      logger.info('Loading dev server URL:', { url: VITE_DEV_SERVER_URL }, 'system');
      this.mainWindow.loadURL(VITE_DEV_SERVER_URL).catch(err => {
        logger.error('Failed to load dev server URL:', { error: err.message }, 'system');
        // Fallback to local file if dev server fails
        const indexPath = path.join(appPath, 'dist/index.html');
        logger.info('Falling back to local file:', { indexPath }, 'system');
        this.mainWindow.loadFile(indexPath);
      });
      // this.mainWindow.webContents.openDevTools();
    } else {
      const indexPath = path.join(appPath, 'dist/index.html');
      logger.info('Loading index file:', { indexPath }, 'system');

      // Sử dụng protocol file:// để tải file index.html
      this.mainWindow.loadFile(indexPath);

      // Đăng ký sự kiện để log lỗi nếu có
      this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        logger.error('Failed to load window:', { errorCode, errorDescription }, 'system');

        // Thử tải lại với đường dẫn tương đối nếu gặp lỗi
        logger.info('Trying to reload with relative path...', {}, 'system');
        this.mainWindow.loadFile(indexPath, { search: '?t=' + Date.now() });
      });

      // Mở DevTools trong production để debug
      // this.mainWindow.webContents.openDevTools();
    }

    // Xử lý sự kiện đóng cửa sổ
    this.mainWindow.on('closed', () => {
      logger.info('Window closed', {}, 'system');
      this.mainWindow = null;
    });

    // Thiết lập mainWindow cho loggerCore sau khi cửa sổ đã được tạo hoàn toàn
    this.mainWindow.webContents.on('did-finish-load', () => {
      logger.info('Window loaded successfully', {}, 'system');
      if (this.mainWindow) {
        loggerCore.setMainWindow(this.mainWindow);
      }
    });

    // Log lỗi renderer process
    this.mainWindow.webContents.on('render-process-gone', (event, details) => {
      logger.error('Renderer process crashed:', { details }, 'system');
    });

    // Log console từ renderer
    this.mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
      logger.info('Renderer console:', { level, message, line, sourceId }, 'system');
    });
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  public showWindow() {
    if (this.mainWindow === null) {
      this.createWindow();
    }
  }

  public closeWindow() {
    if (this.mainWindow) {
      this.mainWindow.close();
    }
  }

  public minimizeWindow() {
    if (this.mainWindow) {
      this.mainWindow.minimize();
    }
  }

  public maximizeWindow() {
    if (this.mainWindow) {
      if (this.mainWindow.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow.maximize();
      }
    }
  }

  public isWindowMaximized(): boolean {
    return this.mainWindow?.isMaximized() || false;
  }

  public sendToRenderer(channel: string, ...args: any[]) {
    if (this.mainWindow?.webContents) {
      this.mainWindow.webContents.send(channel, ...args);
    }
  }
}