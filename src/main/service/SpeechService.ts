import path from 'path';
import {runPythonScript} from './PythonService';
import type {TextToSpeechParams, TTSChunk, TTSProcess} from '@types';
import fs from 'fs';
import logger from '../logger/client';
import {createStore, getStoreValue} from '@main/utils/store-helper';
import {TTSProcessStore} from '../store/TTSProcessStore';
import {TTSQueue} from './TTSQueue';
import {calculateTotalDuration, calculateExecutionTime, calculateProgress} from '@main/utils/TTSProcessUtils';

interface PythonScriptResult {
  error?: string;
  output?: string;
}

export class SpeechService {
  private static instance: SpeechService;
  private store = createStore();
  private log = logger.channel('tts');
  private processStore = TTSProcessStore.getInstance();

  public static getInstance(): SpeechService {
    if (!SpeechService.instance) {
      SpeechService.instance = new SpeechService();
    }
    return SpeechService.instance;
  }

  /**
   * <PERSON>yển đổi văn bản thành giọng nói
   * @param params Tham số đầu vào cho quá trình chuyển đổi
   * @returns Thông tin về quá trình xử lý và đường dẫn file output
   */
  public async textToSpeech(params: TextToSpeechParams) {
    try {
      this.log.info("Bắt đầu quá trình chuyển đổi văn bản thành giọng nói", {...params});
      const processId = params.processId

      // Lấy process từ store
      let process = this.processStore.getProcess(processId) as TTSProcess | undefined;
      if (!process) {
        this.log.error(`Process not found: ${processId}`);
        return;
      }
      if (!process.audioPath) {
        process.audioPath = path.join(params.outputPath, `${params.fileName}.wav`);
      }

      if (process.startTime) {
        process.reloadTime = new Date().toISOString();
      } else {
        process.startTime = new Date().toISOString();
      }

      // Chỉ split text thành chunks khi process.chunks là rỗng
      if (!process.chunks || process.chunks.length === 0) {
        process.status = 'processing';
        process.progress = 0;
        process = this.processStore.updateProcess(process);

        const chunksDir = this.prepareChunksDir(params);
        const chunks = await this.splitTextIntoChunks(params.text, params.language, processId);
        process.chunks = chunks.map((text, index) => ({
          id: `${processId}_chunk_${index + 1}`,
          text,
          status: 'pending',
          audioPath: path.join(chunksDir, `chunk_${index + 1}.wav`),
          duration: 0
        }));
        process.status = 'running'
      } else {
        process.status = 'running';
        process.progress = calculateProgress(process);
      }
      process = this.processStore.updateProcess(process);

      // 3. Xử lý từng đoạn văn bản
      // Xử lý các chunks chưa completed
      const pendingChunks = process.chunks.filter(chunk => chunk.status !== 'completed');
      if (pendingChunks.length > 0) {
        await this.processChunks(pendingChunks, params, processId);
      }

      // Kiểm tra nếu tất cả chunk đã hoàn thành
      process = this.processStore.getProcess(processId)
      if (process && process.chunks.every(chunk => chunk.status === 'completed')) {
        process.duration = calculateTotalDuration(process.chunks);
        process.status = 'saving';
        process = this.processStore.updateProcess(process);

        await this.mergeAudioFiles(process);
      }
    } catch (error) {
      this.log.error('Error running text-to-speech:', error);
    }
  }

  /**
   * Chuẩn bị các file và thư mục cần thiết cho quá trình xử lý
   * @param params Tham số đầu vào
   * @returns Thông tin về các đường dẫn file và thư mục
   */
  private prepareChunksDir(params: TextToSpeechParams): string {
    // Tạo thư mục gen_voice_chunks trong outputPath
    const totalChunksDir = path.join(params.outputPath, 'gen_voice_chunks');
    fs.mkdirSync(totalChunksDir, {recursive: true});

    // Tạo thư mục con cho process theo fileName
    const chunksDir = path.join(totalChunksDir, params.fileName);
    fs.mkdirSync(chunksDir, {recursive: true});

    return chunksDir;
  }

  /**
   * Tách văn bản thành các đoạn nhỏ để xử lý
   * @param text Văn bản cần tách
   * @param language Ngôn ngữ của văn bản
   * @param processId ID của tiến trình
   * @returns Mảng các đoạn văn bản đã tách
   */
  public async splitTextIntoChunks(text: string, language: string, processId: string): Promise<string[]> {
    this.log.debug("Bắt đầu tách text", {text: text.slice(0, 50) + "...", language});
    const outputPathJson = path.join(getStoreValue(this.store, 'tempDir') as string, `tts_${processId}`, 'chunks.json');
    fs.mkdirSync(path.dirname(outputPathJson), {recursive: true});
    fs.writeFileSync(outputPathJson, '[]', 'utf-8');
    // Thay thế toàn bộ ký tự " và dấu xuống dòng trong text thành rỗng
    const safeText = text.replace(/["\n\r]/g, '');

    const splitResult = await runPythonScript('text_utils', [
      '--text', JSON.stringify(safeText),
      '--output', outputPathJson,
      '--language', language,
      '--process_id', processId
    ], {}, processId) as PythonScriptResult;

    if (splitResult.error) {
      this.log.error("Lỗi khi tách text", {error: splitResult.error});
      throw new Error(splitResult.error);
    }

    const jsonContent = fs.readFileSync(outputPathJson, 'utf-8');
    const chunks = JSON.parse(jsonContent);

    if (!chunks || chunks.length === 0) {
      throw new Error('Không thể tách text thành các đoạn');
    }
    return chunks;
  }

  /**
   * Xử lý các đoạn văn bản thành file âm thanh
   * @param chunks Các đoạn văn bản cần xử lý
   * @param params Tham số đầu vào
   * @param chunksDir Thư mục lưu trữ chunks
   * @param processId
   * @returns Mảng đường dẫn các file âm thanh đã tạo
   */
  private async processChunks(chunks: TTSChunk[], params: TextToSpeechParams, processId: string): Promise<string[]> {
    this.log.debug("Bắt đầu xử lý các đoạn", {numChunks: chunks.length});

    // Lấy process đúng kiểu TTSProcess
    let process = this.processStore.getProcess(processId);

    // 1. Tạo args_list
    const argsList = chunks.map((chunk: TTSChunk) => {
      return this.buildTTSArgs({
        ...params,
        text: chunk.text,
        outputPath: chunk.audioPath,
        processId,
        chunkId: chunk.id
      });
    });

    // 2. Tạo object input để serialize
    const processorInput = {
      chunks,
      num_workers: params.numWorkers || 1,
      args_list: argsList
    };

    await runPythonScript('tts_processor',
        [
          JSON.stringify(processorInput),
          '--process_id', processId
        ],
        {
          onProgress: (data) => {
            process = this.processStore.getProcess(processId);
            if (process) {
              const chunkIndex = process.chunks.findIndex(c => c.id === data.chunk_info.id);
              if (chunkIndex !== -1) {
                process.chunks[chunkIndex] = {
                  ...process.chunks[chunkIndex],
                  ...data.chunk_info
                }
                process.progress = calculateProgress(process)
                process = this.processStore.updateProcess(process);
              }
            }
          }
        }, processId) as PythonScriptResult;

    const finalProcessState = this.processStore.getProcess(processId);
    if (finalProcessState && finalProcessState.chunks.every(c => c.status === 'completed')) {
      const outputPaths = finalProcessState.chunks.map(c => c.audioPath!);
      this.log.debug("Đã xử lý các đoạn thành công, lấy kết quả từ store.", {numOutputs: outputPaths.length});
      return outputPaths;
    } else {
      this.log.error("Quá trình xử lý chunk không hoàn tất hoặc không tìm thấy process.", {processId});
      throw new Error("Chunk processing did not complete successfully.");
    }
  }

  /**
   * Tạo danh sách tham số cho lệnh chuyển văn bản thành giọng nói
   * @param params Tham số đầu vào
   * @returns Mảng các tham số dòng lệnh
   */
  private buildTTSArgs(params: TextToSpeechParams): string[] {
    const args = [
      '--text', params.text,
      '--output', params.outputPath,
      '--model_path', params.voice.modelPath,
      '--language', params.language || 'en',
      '--speed', (params.voice.speed ?? 1.0).toString(),
      '--temperature', (params.voice.temperature ?? 0.4).toString(),
      '--repetition_penalty', (params.voice.repetitionPenalty ?? 4.0).toString(),
      '--top_k', (params.voice.topK ?? 25).toString(),
      '--top_p', (params.voice.topP ?? 0.65).toString(),
      '--length_penalty', (params.voice.lengthPenalty ?? 1.0).toString(),
      '--num_threads', (params.numThreads ?? 1).toString(),
      '--process_id', params.processId || '',
      '--chunk_id', params.chunkId || ''
    ];

    // Thêm speaker_wav nếu có referencePaths
    if (params.voice.referencePaths && params.voice.referencePaths.length > 0) {
      // Kiểm tra và chuẩn hóa đường dẫn file
      const validPaths = params.voice.referencePaths.filter(p => {
        const exists = fs.existsSync(p);
        if (!exists) {
          this.log.warning(`File not found: ${p}`);
        }
        return exists;
      });

      if (validPaths.length === 0) {
        throw new Error('No valid reference audio files found');
      }

      // Thêm từng file một cách riêng biệt
      validPaths.forEach(p => {
        args.push('--speaker_wav', p);
      });
    } else {
      throw new Error('Voice reference path is required');
    }

    // Thêm các tham số tùy chọn
    if (params.voice.configPath) {
      args.push('--config_path', params.voice.configPath);
    }
    if (params.voice.vocabPath) {
      args.push('--vocab_path', params.voice.vocabPath);
    }

    this.log.debug("Đã tạo args cho TTS", {args});
    return args;
  }

  private async mergeAudioFiles(process: TTSProcess): Promise<void> {
    this.log.debug("Bắt đầu gộp các file âm thanh cuar process", {id: process.id});
    const inputPaths = process.chunks.map(chunk => chunk.audioPath);
    const mergeResult = await runPythonScript(
        'audio_utils',
        [
          '--input', JSON.stringify(inputPaths),
          '--output', process.audioPath,
          '--sample_rate', '24000',
          '--gap_duration', '0.1'
        ],
        {},
        process.id
    ) as PythonScriptResult;

    if (mergeResult.error) {
      this.log.error("Lỗi khi gộp file âm thanh", {error: mergeResult.error});
      process.status = 'failed';
      process.executionTime = calculateExecutionTime(process);
      this.processStore.updateProcess(process);
      return;
    }
    process.progress = 100;
    process.status = 'completed';
    process.executionTime = calculateExecutionTime(process);
    process.duration = calculateTotalDuration(process.chunks);
    this.processStore.updateProcess(process);
  }

  /**
   * Lấy thời lượng của file âm thanh
   * @param audioPath Đường dẫn đến file âm thanh
   * @param processId Process ID cho logging (optional)
   * @returns Thời lượng tính bằng giây
   */
  public async getAudioDuration(audioPath: string, processId?: string): Promise<number> {
    return new Promise((resolve, _) => {
      let resolved = false;
      const args = ['--audio_path', audioPath];

      // Thêm processId nếu có
      if (processId) {
        args.push('--process_id', processId);
      }

      runPythonScript('duration', args, {
        onComplete: (data) => {
          if (resolved) return;
          // Sử dụng onComplete để nhận duration từ log.data với type: 'complete'
          if (data && typeof data.duration === 'number') {
            this.log.debug('Lấy duration thành công từ onComplete', {audioPath, duration: data.duration, processId});
            resolved = true;
            resolve(data.duration);
          } else {
            this.log.error('Dữ liệu duration không hợp lệ từ onComplete', {audioPath, data, processId});
            resolved = true;
            resolve(0);
          }
        },
        onError: (error) => {
          if (resolved) return;
          this.log.error('Lỗi từ duration service', {audioPath, error, processId});
          resolved = true;
          resolve(0);
        }
      }, processId).then(() => {
        // Timeout fallback nếu không nhận được data
        setTimeout(() => {
          if (!resolved) {
            this.log.error('Timeout: Không nhận được duration từ service', {audioPath, processId});
            resolved = true;
            resolve(0);
          }
        }, 5000);
      }).catch((error) => {
        if (resolved) return;
        this.log.error('Lỗi khi chạy duration script', {audioPath, error, processId});
        resolved = true;
        resolve(0);
      });
    });
  }

  /**
   * Reload một chunk và tự động gộp lại với các chunks khác
   * @returns Kết quả của quá trình reload
   * @param params
   */
  public async reloadChunk(params: { processId: string; chunkId: string; text: string }) {
    this.log.info('Bắt đầu reload chunk', params);

    try {
      // Get process and chunk info
      let process = this.processStore.getProcess(params.processId);
      if (!process) {
        this.log.error(`Process not found: ${params.processId} in SpeechService line 412`);
        return;
      }

      let chunk = process.chunks?.find(c => c.id === params.chunkId);
      if (!chunk) {
        this.log.error(`Chunk not found: ${params.chunkId} in SpeechService line 418`);
        return;
      }

      const oldText = chunk.text;

      // Check memory before processing
      const ttsQueue = TTSQueue.getInstance();
      await ttsQueue.checkMemoryAndPauseIfNeeded();

      try {
        // Update chunk status
        chunk.status = 'running';
        chunk.text = params.text;
        process.status = 'running';
        process = this.processStore.updateProcess(process);

        // Build TTS args
        const args = this.buildTTSArgs({
          text: chunk.text,
          outputPath: chunk.audioPath,
          voice: process.voice,
          language: 'en',
          processId: process.id,
          fileName: process.fileName,
          chunkId: chunk.id
        });

        // Execute TTS
        const result = await runPythonScript('tts', args, {}, process.id) as PythonScriptResult;
        if (result.error) {
          this.log.error('Lỗi khi chạy tts script', {error: result.error});
          process = this.processStore.getProcess(params.processId);
          if (!process) {
            this.log.error(`Process not found: ${params.processId} in SpeechService line 454`);
            return;
          }
          chunk = process.chunks?.find(c => c.id === params.chunkId);
          if (!chunk) {
            this.log.error(`Chunk not found: ${params.chunkId} in SpeechService line 459`);
            return;
          }
          chunk.status = 'failed';
          chunk.error = result.error;
          chunk.text = oldText;
          process.status = 'failed';
          process.error = `Reload chunk ${chunk.id} failed: ${result.error}`;
          this.processStore.updateProcess(process);
          return;
        }

        // Get duration
        const duration = await this.getAudioDuration(chunk.audioPath, process.id);

        process = this.processStore.getProcess(params.processId);
        if (!process) {
          this.log.error(`Process not found: ${params.processId} in SpeechService line 478`);
          return;
        }
        chunk = process.chunks?.find(c => c.id === params.chunkId);
        if (!chunk) {
          this.log.error(`Chunk not found: ${params.chunkId} in SpeechService line 483`);
          return;
        }
        // Update chunk info
        chunk.status = 'completed';
        chunk.duration = duration;
        chunk.error = undefined;

        // Merge audio if all chunks are completed
        if (process.chunks.every(chunk => chunk.status === 'completed')) {
          process.status = 'saving';
          process.error = undefined;
          process = this.processStore.updateProcess(process);
          await this.mergeAudioFiles(process);
        } else {
          this.processStore.updateProcess(process);
        }

        await ttsQueue.resumeQueue();

        return {success: true};
      } catch (error) {
        chunk.status = 'failed';
        chunk.error = String(error);
        chunk.text = oldText;
        process.status = 'failed';
        process.error = String(error);
        process = this.processStore.updateProcess(process);

        await ttsQueue.resumeQueue();

        throw error;
      }
    } catch (error) {
      this.log.error('Error reloading chunk:', error);
      return {error: String(error)};
    }
  }

}