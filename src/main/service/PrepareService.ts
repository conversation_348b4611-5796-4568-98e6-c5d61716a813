import { app } from 'electron';
import path from 'path';
import fs from 'fs';
import type { Voice } from '@types';
import { getVoiceStore } from '@main/store/VoiceStore';
import { logger } from '@main/logger/client';
import { setStoreValue } from '@main/utils/store-helper';

export class PrepareService {
  private static instance: PrepareService;
  private voiceStore: ReturnType<typeof getVoiceStore>;
  private readonly resourcesPath: string;
  private readonly userDataPath: string;
  private readonly commonVoicesPath: string;

  private constructor() {
    this.voiceStore = getVoiceStore();
    this.resourcesPath = app.isPackaged
      ? path.join(process.resourcesPath, 'resources')
      : path.join(__dirname, '../resources');
    this.userDataPath = app.getPath('userData');
    this.commonVoicesPath = path.join(this.userDataPath, 'voices', 'common');
  }

  public static getInstance(): PrepareService {
    if (!PrepareService.instance) {
      PrepareService.instance = new PrepareService();
    }
    return PrepareService.instance;
  }

  /**
   * Copy các file wav từ resources vào thư mục common
   */
  private async copyWavFiles(): Promise<void> {
    try {
      // Tạo thư mục common nếu chưa tồn tại
      if (!fs.existsSync(this.commonVoicesPath)) {
        fs.mkdirSync(this.commonVoicesPath, { recursive: true });
        logger.info(`Đã tạo thư mục common: ${this.commonVoicesPath}`);
      }

      // Đọc danh sách file wav từ thư mục resources/voice
      const sourceDir = path.join(this.resourcesPath, 'voice');
      const files = fs.readdirSync(sourceDir);
      const wavFiles = files.filter(file => file.endsWith('.wav'));

      // Copy từng file wav
      for (const file of wavFiles) {
        const sourcePath = path.join(sourceDir, file);
        const destPath = path.join(this.commonVoicesPath, file);
        
        // Chỉ copy nếu file đích chưa tồn tại
        if (!fs.existsSync(destPath)) {
          fs.copyFileSync(sourcePath, destPath);
          logger.info(`Đã copy file: ${file}`);
        }
      }
    } catch (error) {
      logger.error('Lỗi khi copy file wav:', error);
      throw error;
    }
  }

  /**
   * Cập nhật đường dẫn cho các voice
   */
  private updateVoicePaths(voices: Voice[]): Voice[] {
    return voices.map(voice => {
      // Cập nhật đường dẫn reference
      const updatedReferencePaths = voice.referencePaths.map(ref => 
        path.join(this.commonVoicesPath, path.basename(ref))
      );

      // Cập nhật đường dẫn preview
      const updatedPreviewPath = path.join(this.commonVoicesPath, path.basename(voice.previewPath));

      return {
        ...voice,
        referencePaths: updatedReferencePaths,
        previewPath: updatedPreviewPath
      };
    });
  }

  /**
   * Khởi tạo các built-in voices từ file voices.json
   */
  public async initializeBuiltinVoices(): Promise<void> {
    try {
      logger.info('Bắt đầu khởi tạo built-in voices');
      
      // Đọc file voices.json từ thư mục resources
      const voicesJsonPath = path.join(this.resourcesPath, 'voice', 'voices.json');
      logger.debug('Vị trí file voices.json: ' + voicesJsonPath)
      if (!fs.existsSync(voicesJsonPath)) {
        logger.error('Không tìm thấy file voices.json trong thư mục resources');
        return;
      }

      const voicesJson = JSON.parse(fs.readFileSync(voicesJsonPath, 'utf-8'));
      logger.debug("Voices Json: ", voicesJson)
      const { voices, defaultVoice } = voicesJson;

      // Kiểm tra xem store đã có voices chưa
      const currentVoices = this.voiceStore.getVoices();
      if (currentVoices.length > 0) {
        logger.info('Store đã có voices, bỏ qua việc khởi tạo built-in voices');
        return;
      }

      // Copy các file wav vào thư mục common
      await this.copyWavFiles();

      // Cập nhật đường dẫn cho các voice
      const updatedVoices = this.updateVoicePaths(voices);

      // Thêm các built-in voices vào store
      setStoreValue(this.voiceStore['store'], 'voices', updatedVoices);
      
      // Đặt default voice
      if (defaultVoice) {
        setStoreValue(this.voiceStore['store'], 'defaultVoice', defaultVoice);
      }

      logger.info('Đã khởi tạo built-in voices thành công');
    } catch (error) {
      logger.error('Lỗi khi khởi tạo built-in voices:', error);
    }
  }
} 