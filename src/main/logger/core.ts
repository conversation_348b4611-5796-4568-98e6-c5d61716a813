import {app, BrowserWindow} from 'electron';
import type { default as Store } from 'electron-store';
import path from 'path';
import fs from 'fs';
import type {LogEntry, LoggerConfig} from "../../types";
import { getStoreValue, setStoreValue, createStore } from '@main/utils/store-helper';

// Định nghĩa các kiểu dữ liệu
export type LogLevel = 'debug' | 'info' | 'warning' | 'error';
export type LogChannel = 'system' | 'metadata' | 'finetune' | 'tts' | 'general' | 'tts-queue' | 'tts-process-store';
export type LogSource = 'node' | 'python' | 'renderer';

// Mặc định cấu hình
const DEFAULT_CONFIG: LoggerConfig = {
  maxLogEntries: 1000,
  logToFile: true,
  logFilePath: path.join(app.getPath('userData'), 'logs'),
  logToConsole: true,
  minLevel: 'debug',
};

// Khởi tạo electron-store cho logger
const logStore = createStore({
  name: 'logger',
  defaults: {
    config: DEFAULT_CONFIG,
    logs: [],
  },
});

/**
 * Logger Core - Quản lý trung tâm các log
 */
class LoggerCore {
  private config: LoggerConfig;
  private logs: LogEntry[];
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    // Tải cấu hình từ store
    this.config = getStoreValue(logStore, 'config') as LoggerConfig;

    // Tải logs từ store
    this.logs = getStoreValue(logStore, 'logs') as LogEntry[];

    // Đảm bảo số lượng logs không vượt quá giới hạn
    if (this.logs.length > this.config.maxLogEntries) {
      this.logs = this.logs.slice(-this.config.maxLogEntries);
      this.saveLogs();
    }

    // Tạo thư mục log nếu cần
    if (this.config.logToFile) {
      fs.mkdirSync(this.config.logFilePath, {recursive: true});
    }
  }

  /**
   * Thiết lập cửa sổ chính để gửi log
   */
  setMainWindow(window: BrowserWindow) {
    this.mainWindow = window;
  }

  /**
   * Thêm một log mới
   */
  log(entry: Omit<LogEntry, 'timestamp'>) {
    // Xử lý data để đảm bảo có thể serialize được
    let processedData = entry.data;
    if (entry.data !== undefined) {
      try {
        // Kiểm tra xem data có thể serialize được không
        JSON.stringify(entry.data);
      } catch (error) {
        // Nếu không thể serialize, chuyển thành string
        processedData = {
          serialized: `[Object không thể serialize: ${typeof entry.data}]`,
          toString: String(entry.data)
        };
      }
    }

    // Tạo log entry đầy đủ
    const fullEntry: LogEntry = {
      ...entry,
      data: processedData,
      timestamp: new Date().toISOString(),
    };

    // Kiểm tra level
    if (!this.shouldLog(fullEntry.level)) {
      return;
    }

    // Thêm vào danh sách logs
    this.logs.push(fullEntry);

    // Giới hạn số lượng logs
    if (this.logs.length > this.config.maxLogEntries) {
      this.logs.shift();
    }

    // Lưu logs vào store
    this.saveLogs();

    // Ghi log ra console nếu được cấu hình
    if (this.config.logToConsole) {
      this.logToConsole(fullEntry);
    }

    // Ghi log ra file nếu được cấu hình
    if (this.config.logToFile) {
      this.logToFile(fullEntry);
    }

    // Gửi log đến renderer process
    this.sendLogToRenderer(fullEntry);
  }

  /**
   * Lấy tất cả logs
   */
  getLogs(filter?: {
    level?: LogLevel;
    channel?: LogChannel;
    source?: LogSource;
    processId?: string;
    startTime?: string;
    endTime?: string;
  }): LogEntry[] {
    // Lọc logs theo filter
    let filteredLogs = this.logs;

    if (filter) {
      filteredLogs = this.logs.filter((log) => {
        if (filter.level && log.level !== filter.level) {
          return false;
        }
        if (filter.channel && log.channel !== filter.channel) {
          return false;
        }
        if (filter.source && log.source !== filter.source) {
          return false;
        }
        if (filter.processId && log.processId !== filter.processId) {
          return false;
        }
        if (filter.startTime && log.timestamp < filter.startTime) {
          return false;
        }
        return !(filter.endTime && log.timestamp > filter.endTime);

      });
    }

    // Đảm bảo các đối tượng có thể serialize được
    return filteredLogs.map(log => {
      // Tạo bản sao của log
      const serializedLog: LogEntry = {
        timestamp: log.timestamp,
        level: log.level,
        channel: log.channel,
        message: log.message,
        source: log.source,
        processId: log.processId
      };

      // Xử lý data để đảm bảo có thể serialize được
      if (log.data !== undefined) {
        try {
          // Kiểm tra xem data có thể serialize được không
          JSON.stringify(log.data);
          serializedLog.data = log.data;
        } catch (error) {
          // Nếu không thể serialize, chuyển thành string
          serializedLog.data = {
            serialized: `[Object không thể serialize: ${typeof log.data}]`,
            toString: String(log.data)
          };
        }
      }

      return serializedLog;
    });
  }

  /**
   * Xóa tất cả logs
   */
  clearLogs() {
    this.logs = [];
    this.saveLogs();
  }

  /**
   * Cập nhật cấu hình
   */
  updateConfig(config: Partial<LoggerConfig>) {
    this.config = {...this.config, ...config};
    setStoreValue(logStore, 'config', this.config);

    // Tạo thư mục log nếu cần
    if (this.config.logToFile) {
      fs.mkdirSync(this.config.logFilePath, {recursive: true});
    }
  }

  /**
   * Lấy cấu hình hiện tại
   */
  getConfig(): LoggerConfig {
    return {...this.config};
  }

  /**
   * Kiểm tra xem có nên log level này không
   */
  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warning', 'error'];
    const minLevelIndex = levels.indexOf(this.config.minLevel);
    const currentLevelIndex = levels.indexOf(level);
    return currentLevelIndex >= minLevelIndex;
  }

  /**
   * Lưu logs vào store
   */
  private saveLogs() {
    try {
      // Đảm bảo logs có thể serialize được trước khi lưu vào store
      const serializedLogs = this.logs.map(log => {
        // Tạo bản sao của log
        const serializedLog: LogEntry = {
          timestamp: log.timestamp,
          level: log.level,
          channel: log.channel,
          message: log.message,
          source: log.source,
          processId: log.processId
        };

        // Xử lý data để đảm bảo có thể serialize được
        if (log.data !== undefined) {
          try {
            // Kiểm tra xem data có thể serialize được không
            JSON.stringify(log.data);
            serializedLog.data = log.data;
          } catch (error) {
            // Nếu không thể serialize, chuyển thành string
            serializedLog.data = {
              serialized: `[Object không thể serialize: ${typeof log.data}]`,
              toString: String(log.data)
            };
          }
        }

        return serializedLog;
      });

      setStoreValue(logStore, 'logs', serializedLogs);
    } catch (error) {
      console.error('Lỗi khi lưu logs vào store:', error);
    }
  }

  /**
   * Ghi log ra console
   */
  private logToConsole(entry: LogEntry) {
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const prefix = `${timestamp} - ${entry.level.toUpperCase()} - ${entry.channel?.toUpperCase()} :`;

    switch (entry.level) {
      case 'debug':
        console.debug(`${prefix} ${entry.message}`, entry.data || '');
        break;
      case 'info':
        console.info(`${prefix} ${entry.message}`, entry.data || '');
        break;
      case 'warning':
        console.warn(`${prefix} ${entry.message}`, entry.data || '');
        break;
      case 'error':
        console.error(`${prefix} ${entry.message}`, entry.data || '');
        break;
    }
  }

  /**
   * Ghi log ra file
   */
  private logToFile(entry: LogEntry) {
    try {
      const date = new Date(entry.timestamp).toISOString().split('T')[0];
      const logFile = path.join(this.config.logFilePath, `${date}.log`);

      const logLine = `${entry.timestamp} [${entry.level.toUpperCase()}] [${entry.channel}] [${entry.source}] ${entry.message}\n`;

      fs.appendFileSync(logFile, logLine);
    } catch (error) {
      console.error('Lỗi khi ghi log ra file:', error);
    }
  }

  /**
   * Gửi log đến renderer process
   */
  private sendLogToRenderer(entry: LogEntry) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      // Đảm bảo log có thể serialize được trước khi gửi
      const serializedEntry: LogEntry = {
        timestamp: entry.timestamp,
        level: entry.level,
        channel: entry.channel,
        message: entry.message,
        source: entry.source,
        processId: entry.processId
      };

      // Xử lý data để đảm bảo có thể serialize được
      if (entry.data !== undefined) {
        try {
          // Kiểm tra xem data có thể serialize được không
          JSON.stringify(entry.data);
          serializedEntry.data = entry.data;
        } catch (error) {
          // Nếu không thể serialize, chuyển thành string
          serializedEntry.data = {
            serialized: `[Object không thể serialize: ${typeof entry.data}]`,
            toString: String(entry.data)
          };
        }
      }

      this.mainWindow.webContents.send('log-entry', serializedEntry);
    }
  }
}

// Tạo instance của LoggerCore
export const loggerCore = new LoggerCore();

export default loggerCore;
