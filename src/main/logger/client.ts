import type {LogChannel, LogLevel} from './core';
import type {LogEntry} from '../../types';
import loggerCore from './core';

/**
 * Logger Client - API để ghi log từ main process
 */
class LoggerClient {
  private source: 'node' | 'python' | 'renderer';
  private defaultChannel: LogChannel;
  private processId?: string;

  constructor(source: 'node' | 'python' | 'renderer' = 'node', defaultChannel: LogChannel = 'general', processId?: string) {
    this.source = source;
    this.defaultChannel = defaultChannel;
    this.processId = processId;
  }

  /**
   * Ghi log debug
   */
  debug(message: string, data?: any, channel?: LogChannel) {
    this.log('debug', message, data, channel);
    return this;
  }

  /**
   * Ghi log info
   */
  info(message: string, data?: any, channel?: LogChannel) {
    this.log('info', message, data, channel);
    return this;
  }

  /**
   * Ghi log warning
   */
  warning(message: string, data?: any, channel?: LogChannel) {
    this.log('warning', message, data, channel);
    return this;
  }

  /**
   * Ghi log error
   */
  error(message: string, data?: any, channel?: LogChannel) {
    this.log('error', message, data, channel);
    return this;
  }

  /**
   * Ghi log với level tùy chọn
   */
  log(level: LogLevel, message: string, data?: any, channel?: LogChannel, type?: string) {
    const entry: Omit<LogEntry, 'timestamp'> = {
      level,
      channel: channel || this.defaultChannel,
      message,
      data,
      source: this.source,
      processId: this.processId,
      type: type,
    };

    loggerCore.log(entry);
    return this;
  }

  /**
   * Tạo một logger mới với channel khác
   */
  channel(channel: LogChannel) {
    return new LoggerClient(this.source, channel, this.processId);
  }
}

// Tạo instance mặc định của LoggerClient
export const logger = new LoggerClient('node', 'system');

export default logger;
