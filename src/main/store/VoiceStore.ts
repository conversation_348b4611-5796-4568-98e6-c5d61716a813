import {app} from 'electron';
import type { default as Store } from 'electron-store';
import path from 'path';
import fs from 'fs';
import {v4 as uuidv4} from 'uuid';
import type {Voice, VoiceStoreInterface} from '@types';
import {logger} from '@main/logger/client';
import { getStoreValue, setStoreValue, deleteStoreValue, createStore } from '@main/utils/store-helper';

// Class VoiceStore
export class VoiceStore {
  private store: Store<VoiceStoreInterface>;
  private readonly appDataPath: string;
  private readonly voicesPath: string;

  constructor() {
    this.store = createStore<VoiceStoreInterface>({
      name: 'voices',
      defaults: {
        voices: [],
      },
    });

    // Đường dẫn đến thư mục dữ liệu của ứng dụng
    this.appDataPath = app.getPath('userData');
    this.voicesPath = path.join(this.appDataPath, 'voices');

    // <PERSON><PERSON><PERSON> thư mục voices nếu chưa tồn tại
    if (!fs.existsSync(this.voicesPath)) {
      fs.mkdirSync(this.voicesPath, {recursive: true});
    }

  }

  // <PERSON><PERSON><PERSON> tất cả voices
  getVoices(): Voice[] {
    return getStoreValue<VoiceStoreInterface, 'voices'>(this.store, 'voices');
  }

  // Lấy voice theo id
  getVoice(id: string): Voice | null {
    const voices = getStoreValue<VoiceStoreInterface, 'voices'>(this.store, 'voices');
    return voices.find((voice) => voice.id === id) || null;
  }

  // Thêm voice mới
  addVoice(voice: Voice): void {
    const voices = getStoreValue<VoiceStoreInterface, 'voices'>(this.store, 'voices');
    const id = voice.id || uuidv4();

    voices.push({
      ...voice,
      id,
    });

    setStoreValue(this.store, 'voices', voices);
  }

  // Cập nhật voice
  async updateVoice(updatedVoice: Voice): Promise<Voice> {
    try {
      logger.info(`Bắt đầu cập nhật voice với id: ${updatedVoice.id}`);
      const voices = getStoreValue<VoiceStoreInterface, 'voices'>(this.store, 'voices');
      const index = voices.findIndex((v) => v.id === updatedVoice.id);
      const oldVoice = voices[index];

      if (index === -1) {
        logger.error(`Không tìm thấy voice với id ${updatedVoice.id}`);
        throw new Error('Voice not found');
      }

      logger.info('Thông tin voice cũ:', {
        ...oldVoice,
      });

      logger.info('Thông tin voice mới:', {
        ...updatedVoice,
      });

      // Xử lý các file mới được chọn
      const voiceDir = path.join(this.voicesPath, updatedVoice.name);
      if (!fs.existsSync(voiceDir)) {
        fs.mkdirSync(voiceDir, { recursive: true });
        logger.info(`Đã tạo thư mục voice: ${voiceDir}`);
      }

      // Copy các file mới nếu có thay đổi
      if (updatedVoice.modelPath !== oldVoice.modelPath) {
        const destPath = path.join(voiceDir, 'model.pth');
        fs.copyFileSync(updatedVoice.modelPath, destPath);
        updatedVoice.modelPath = destPath;
        logger.info(`Đã cập nhật model file: ${destPath}`);
      }

      if (updatedVoice.configPath !== oldVoice.configPath) {
        const destPath = path.join(voiceDir, 'config.json');
        fs.copyFileSync(updatedVoice.configPath, destPath);
        updatedVoice.configPath = destPath;
        logger.info(`Đã cập nhật config file: ${destPath}`);
      }

      if (updatedVoice.vocabPath !== oldVoice.vocabPath) {
        const destPath = path.join(voiceDir, 'vocab.json');
        fs.copyFileSync(updatedVoice.vocabPath, destPath);
        updatedVoice.vocabPath = destPath;
        logger.info(`Đã cập nhật vocab file: ${destPath}`);
      }

      // Xử lý reference files
      if (JSON.stringify(updatedVoice.referencePaths) !== JSON.stringify(oldVoice.referencePath)) {
        logger.info('Cập nhật reference files');

        // Lấy danh sách các file reference cũ và mới
        const oldRefs = oldVoice.referencePaths || [];
        const newRefs = updatedVoice.referencePaths || [];

        // Tìm các file cần xóa (có trong oldRefs nhưng không có trong newRefs)
        const filesToDelete = oldRefs.filter(oldRef => {
          const oldFileName = path.basename(oldRef);
          return !newRefs.some(newRef => path.basename(newRef) === oldFileName);
        });

        // Xóa các file không còn trong danh sách mới
        for (const ref of filesToDelete) {
          if (fs.existsSync(ref)) {
            fs.unlinkSync(ref);
            logger.info(`Đã xóa file reference không còn sử dụng: ${ref}`);
          }
        }

        // Copy các file reference mới và giữ nguyên tên file
        const newReferencePaths = newRefs.map(ref => {
          const fileName = path.basename(ref);
          const destPath = path.join(voiceDir, fileName);

          // Chỉ copy nếu file nguồn khác với file đích
          if (ref !== destPath) {
            // Kiểm tra xem file nguồn có tồn tại không
            if (fs.existsSync(ref)) {
              fs.copyFileSync(ref, destPath);
              logger.info(`Đã copy file reference mới: ${ref} -> ${destPath}`);
            } else {
              logger.warning(`Không tìm thấy file nguồn: ${ref}`);
            }
          }
          return destPath;
        });

        // Cập nhật đường dẫn mới
        updatedVoice.referencePaths = newReferencePaths;
      }

      // Xử lý preview file
      if (updatedVoice.previewPath !== oldVoice.previewPath) {
        if (oldVoice.previewPath && fs.existsSync(oldVoice.previewPath)) {
          fs.unlinkSync(oldVoice.previewPath);
          logger.info(`Đã xóa file preview cũ: ${oldVoice.previewPath}`);
        }
        if (updatedVoice.previewPath) {
          const destPath = path.join(voiceDir, 'preview.wav');
          fs.copyFileSync(updatedVoice.previewPath, destPath);
          updatedVoice.previewPath = destPath;
          logger.info(`Đã cập nhật file preview: ${destPath}`);
        }
      }

      // Nếu tên voice thay đổi, đổi tên thư mục
      if (oldVoice.name !== updatedVoice.name) {
        logger.info(`Tên voice thay đổi từ "${oldVoice.name}" thành "${updatedVoice.name}"`);
        const oldVoiceDir = path.join(this.voicesPath, oldVoice.name);
        const newVoiceDir = path.join(this.voicesPath, updatedVoice.name);

        if (fs.existsSync(oldVoiceDir)) {

          // Tạo thư mục mới nếu chưa tồn tại
          if (!fs.existsSync(newVoiceDir)) {
            fs.mkdirSync(newVoiceDir, { recursive: true });
            logger.info(`Đã tạo thư mục mới: ${newVoiceDir}`);
          }

          // Copy tất cả file từ thư mục cũ sang thư mục mới
          const files = fs.readdirSync(oldVoiceDir);
          for (const file of files) {
            const oldPath = path.join(oldVoiceDir, file);
            const newPath = path.join(newVoiceDir, file);
            fs.copyFileSync(oldPath, newPath);
            logger.info(`Đã copy file: ${oldPath} -> ${newPath}`);
          }

          // Xóa thư mục cũ sau khi đã copy xong
          fs.rmSync(oldVoiceDir, { recursive: true, force: true });
          logger.info(`Đã xóa thư mục cũ: ${oldVoiceDir}`);

          // Cập nhật đường dẫn các file trong updatedVoice
          updatedVoice.modelPath = path.join(newVoiceDir, 'model.pth');
          updatedVoice.configPath = path.join(newVoiceDir, 'config.json');
          updatedVoice.vocabPath = path.join(newVoiceDir, 'vocab.json');
          updatedVoice.referencePaths = (updatedVoice.referencePaths || []).map((ref) =>
            path.join(newVoiceDir, path.basename(ref))
          );
          if (oldVoice.previewPath) {
            updatedVoice.previewPath = path.join(newVoiceDir, 'preview.wav');
          }

          logger.info('Đã cập nhật đường dẫn các file:', {
            modelPath: updatedVoice.modelPath,
            configPath: updatedVoice.configPath,
            vocabPath: updatedVoice.vocabPath,
            referencePaths: updatedVoice.referencePaths,
            previewPath: updatedVoice.previewPath
          });
        }
      }

      updatedVoice = {
        ...oldVoice,
        ...updatedVoice,
        referencePaths: [...(updatedVoice.referencePaths || [])],
        tags: [...(updatedVoice.tags || [])]
      };

      // Cập nhật voice trong store
      voices[index] = updatedVoice;

      setStoreValue(this.store, 'voices', voices);
      return updatedVoice;
    } catch (error) {
      logger.error('Lỗi khi cập nhật voice:', error);
      throw error;
    }
  }

  // Xóa voice
  async deleteVoice(id: string): Promise<boolean> {
    try {
      const voices = getStoreValue<VoiceStoreInterface, 'voices'>(this.store, 'voices');
      const voice = voices.find((v) => v.id === id);

      if (!voice) {
        return false;
      }

      // Xóa thư mục voice nếu là voice được import hoặc finetune
      if (voice.type === 'imported' || voice.type === 'finetuned') {
        const voiceDir = path.join(this.voicesPath, voice.name);
        if (fs.existsSync(voiceDir)) {
          await fs.promises.rm(voiceDir, { recursive: true, force: true });
          logger.info(`Đã xóa thư mục voice: ${voiceDir}`);
        }
      }

      // Xóa voice khỏi danh sách
      const filteredVoices = voices.filter((v) => v.id !== id);
      setStoreValue(this.store, 'voices', filteredVoices);

      // Nếu voice bị xóa là default voice, xóa default voice
      const defaultVoice = getStoreValue<VoiceStoreInterface, 'defaultVoice'>(this.store, 'defaultVoice');
      if (defaultVoice === id) {
        deleteStoreValue(this.store, 'defaultVoice');
      }

      return true;
    } catch (error) {
      logger.error('Lỗi khi xóa voice:', error);
      return false;
    }
  }

  // Đặt default voice
  setDefaultVoice(id: string): void {
    const voice = this.getVoice(id);

    if (voice) {
      setStoreValue(this.store, 'defaultVoice', id);
    }
  }

  // Lấy default voice
  getDefaultVoice(): Voice | null {
    const defaultVoiceId = getStoreValue<VoiceStoreInterface, 'defaultVoice'>(this.store, 'defaultVoice');

    if (defaultVoiceId) {
      return this.getVoice(defaultVoiceId) || null;
    }

    return null;
  }

  // Import voice từ thư mục
  async importVoice(importDir: string, voiceName?: string): Promise<Voice | null> {
    logger.info(`Bắt đầu import voice từ thư mục: ${importDir}`);
    try {
      // Kiểm tra thư mục import có tồn tại không
      if (!fs.existsSync(importDir)) {
        logger.error(`Thư mục import không tồn tại: ${importDir}`);
        return null;
      }

      // Đọc file voice.json
      const voiceJsonPath = path.join(importDir, 'voice.json');
      if (!fs.existsSync(voiceJsonPath)) {
        logger.error(`Không tìm thấy file voice.json trong thư mục: ${importDir}`);
        return null;
      }

      const voiceJson = JSON.parse(fs.readFileSync(voiceJsonPath, 'utf-8'));

      logger.info('Debug - VoiceStore received:', {
        importDir,
        voiceName,
        type: typeof voiceName
      });

      // Lấy tên voice từ tham số hoặc tên thư mục
      const name = voiceName && voiceName.trim() !== '' ? voiceName : path.basename(importDir);
      logger.info(`Sử dụng tên voice: ${name}`);

      // Tạo thư mục mới cho voice
      const voiceDir = path.join(this.voicesPath, name);
      if (!fs.existsSync(voiceDir)) {
        fs.mkdirSync(voiceDir, { recursive: true });
        logger.info(`Đã tạo thư mục mới cho voice: ${voiceDir}`);
      }

      // Copy các file từ thư mục import sang thư mục mới
      const filesToCopy = [
        { src: path.join(importDir, voiceJson.modelPath), dest: 'model.pth' },
        { src: path.join(importDir, voiceJson.configPath), dest: 'config.json' },
        { src: path.join(importDir, voiceJson.vocabPath), dest: 'vocab.json' },
        ...(voiceJson.referencePaths || []).map((ref: string, index: number) => ({
          src: path.join(importDir, ref),
          dest: `reference_${index + 1}.wav`
        })),
        ...(voiceJson.previewPath ? [{ src: path.join(importDir, voiceJson.previewPath), dest: 'preview.wav' }] : [])
      ];

      for (const file of filesToCopy) {
        if (fs.existsSync(file.src)) {
          fs.copyFileSync(file.src, path.join(voiceDir, file.dest));
          logger.info(`Đã copy file: ${file.src} -> ${path.join(voiceDir, file.dest)}`);
        } else {
          logger.warning(`Không tìm thấy file nguồn: ${file.src}`);
        }
      }

      // Tạo voice mới với đường dẫn tuyệt đối
      const voice: Voice = {
        id: uuidv4(),
        name: name,
        createdAt: voiceJson.createdAt || new Date().toISOString(),
        languages: voiceJson.languages || ['en'],
        modelPath: path.join(voiceDir, 'model.pth'),
        configPath: path.join(voiceDir, 'config.json'),
        vocabPath: path.join(voiceDir, 'vocab.json'),
        referencePaths: (voiceJson.referencePaths || []).map((_: string, index: number) =>
          path.join(voiceDir, `reference_${index + 1}.wav`)
        ),
        previewPath: voiceJson.previewPath ? path.join(voiceDir, 'preview.wav') : '',
        description: voiceJson.description || '',
        tags: voiceJson.tags,
        speed: voiceJson.speed,
        temperature: voiceJson.temperature,
        repetitionPenalty: voiceJson.repetitionPenalty,
        topK: voiceJson.topK,
        topP: voiceJson.topP,
        lengthPenalty: voiceJson.lengthPenalty,
        type: 'imported'
      };

      // Thêm voice vào store
      this.addVoice(voice);
      logger.info(`Đã thêm voice mới vào store: ${voice.id}`);

      return voice;
    } catch (error) {
      logger.error('Lỗi khi import voice:', error);
      return null;
    }
  }

  // Export voice thành file zip
  async exportVoice(id: string, outputPath: string): Promise<boolean> {
    logger.info(`Bắt đầu export voice với id: ${id} và outputPath: ${outputPath}`);
    try {
      const voice = this.getVoice(id);

      if (!voice) {
        logger.error(`Không tìm thấy voice với id ${id}`);
        return false;
      }

      // Tạo thư mục tạm để chứa các file
      const tempDir = path.join(outputPath, voice.name);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
        logger.info(`Đã tạo thư mục tạm: ${tempDir}`);
      }

      // Copy các file cần thiết
      const filesToCopy = [
        { src: voice.modelPath, dest: 'model.pth' },
        { src: voice.configPath, dest: 'config.json' },
        { src: voice.vocabPath, dest: 'vocab.json' },
        ...(voice.referencePaths || []).map((ref, index) => ({
          src: ref,
          dest: `reference_${index + 1}.wav`
        })),
        ...(voice.previewPath ? [{ src: voice.previewPath, dest: 'preview.wav' }] : [])
      ];

      for (const file of filesToCopy) {
        if (file.src && fs.existsSync(file.src)) {
          fs.copyFileSync(file.src, path.join(tempDir, file.dest));
          logger.info(`Đã copy file: ${file.src} -> ${path.join(tempDir, file.dest)}`);
        } else {
          logger.warning(`Không tìm thấy file nguồn: ${file.src}`);
        }
      }

      // Tạo file voice.json với các đường dẫn tương đối
      const voiceJson = {
        ...voice,
        modelPath: 'model.pth',
        configPath: 'config.json',
        vocabPath: 'vocab.json',
        referencePaths: (voice.referencePaths || []).map((_, index) => `reference_${index + 1}.wav`),
        previewPath: voice.previewPath ? 'preview.wav' : undefined
      };

      fs.writeFileSync(
        path.join(tempDir, 'voice.json'),
        JSON.stringify(voiceJson, null, 2)
      );
      logger.info(`Đã tạo file voice.json tại: ${path.join(tempDir, 'voice.json')}`);
      return true;
    } catch (error) {
      logger.error('Lỗi khi export voice:', error);
      return false;
    }
  }
}

// Singleton instance
let voiceStoreInstance: VoiceStore | null = null;

// Hàm để lấy instance của VoiceStore
export function getVoiceStore(): VoiceStore {
  if (!voiceStoreInstance) {
    voiceStoreInstance = new VoiceStore();
  }

  return voiceStoreInstance;
}
