import type {TTSChunk, TTSProcess} from '@types';

/**
 * Tính tổng duration từ các chunk
 * @param chunks Mảng các chunk cần tính duration
 * @returns Tổng duration tính bằng giây
 */
export const calculateTotalDuration = (chunks: TTSChunk[]): number => {
  return chunks.reduce((total, chunk) => total + (chunk.duration || 0), 0);
};

/**
 * Tính thời gian thực thi của process
 * @param process Process cần tính thời gian thực thi
 * @returns Thời gian thực thi tính bằng milliseconds
 */
export const calculateExecutionTime = (process: TTSProcess): number => {
  let executionTime = process.executionTime || 0;
  if (process.reloadTime) {
    // Nếu có reloadTime, cộng thêm thời gian từ reloadTime đến hiện tại
    executionTime += new Date().getTime() - new Date(process.reloadTime).getTime();
  } else if (process.startTime) {
    // Nếu không có reloadTime, tính từ startTime
    executionTime = new Date().getTime() - new Date(process.startTime).getTime();
  }
  return executionTime;
};

/**
 * Tính tiến độ hoàn thành của process
 * @param process Process cần tính tiến độ
 * @returns Tiến độ hoàn thành tính bằng phần trăm
 */
export const calculateProgress = (process: TTSProcess): number => {
  return Math.round((process.chunks.filter(c => c.status === 'completed').length / process.chunks.length) * 100);
};
