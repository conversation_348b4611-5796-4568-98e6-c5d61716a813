import Store from 'electron-store';
import type { default as StoreType } from 'electron-store';

/**
 * Helper functions for electron-store to avoid TypeScript errors
 */

export function getStoreValue<T, K extends keyof T>(store: any, key: string): T[K] {
  return (store as any).get(key);
}

export function setStoreValue<T>(store: any, key: string, value: T): void {
  (store as any).set(key, value);
}

export function deleteStoreValue(store: any, key: string): void {
  (store as any).delete(key);
}

export function createStore<T>(options?: any): StoreType<T> {
  return new Store<T>(options);
}
