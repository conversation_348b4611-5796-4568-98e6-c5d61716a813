import {contextBridge, ipc<PERSON>ender<PERSON>} from 'electron';
import type {LogChannel, LogLevel} from '../main/logger/core';

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho LogEntry
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  channel: LogChannel;
  message: string;
  data?: any;
  source: 'node' | 'python' | 'renderer';
  processId?: string;
}

// Expose logger API cho renderer process
contextBridge.exposeInMainWorld('logger', {
  // Ghi log
  debug: (message: string, data?: any, channel?: LogChannel) => {
    ipcRenderer.invoke('logger-log', 'debug', message, data, channel);
  },

  info: (message: string, data?: any, channel?: LogChannel) => {
    ipcRenderer.invoke('logger-log', 'info', message, data, channel);
  },

  warning: (message: string, data?: any, channel?: LogChannel) => {
    ipcRenderer.invoke('logger-log', 'warning', message, data, channel);
  },

  error: (message: string, data?: any, channel?: LogChannel) => {
    ipcRenderer.invoke('logger-log', 'error', message, data, channel);
  },

  // Lấy logs
  getLogs: (filter?: {
    level?: LogLevel;
    channel?: LogChannel;
    source?: 'node' | 'python' | 'renderer';
    processId?: string;
    startTime?: string;
    endTime?: string;
  }) => {
    return ipcRenderer.invoke('getLogs', filter);
  },

  // Xóa logs
  clearLogs: () => {
    return ipcRenderer.invoke('clearLogs');
  },

  // Lấy cấu hình
  getConfig: () => {
    return ipcRenderer.invoke('getLogConfig');
  },

  // Cập nhật cấu hình
  updateConfig: (config: any) => {
    return ipcRenderer.invoke('updateLogConfig', config);
  },

  // Đăng ký lắng nghe log mới
  onNewLog: (callback: (log: LogEntry) => void) => {
    const listener = (_: any, log: LogEntry) => callback(log);
    ipcRenderer.on('log-entry', listener);
    return () => {
      ipcRenderer.removeListener('log-entry', listener);
    };
  }
});
