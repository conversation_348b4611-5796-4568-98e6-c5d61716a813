#!/usr/bin/env python
# -*- coding: utf-8 -*-

import gc
import json
import os
import re
import subprocess
import traceback
from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
import torchaudio

from logger import get_logger

logger = get_logger(name="metadata_processor", channel="metadata",
                    min_level="info")

# Thử import multilingual_cleaners từ các đường dẫn khác <PERSON>hau (tương thích với nhiều phiên bản TTS)
try:
  # Phiên bản mới
  from TTS.tts.layers.xtts.tokenizer import multilingual_cleaners
except ImportError:
  try:
    # <PERSON><PERSON><PERSON> bản cũ hơn
    from TTS.tts.utils.text.cleaners import multilingual_cleaners
  except ImportError:
    # Fallback: Đ<PERSON>nh nghĩa hàm multilingual_cleaners đơn giản
    def multilingual_cleaners(text):
      # Chỉ loại bỏ khoảng trắng thừa
      return ' '.join(text.split())

# Import module ASR
from asr import transcribe_audio

# Cấu hình mặc định
DEFAULT_CONFIG = {
  "buffer": 0.2,  # khoảng đệm thời gian (giây)
  "eval_percentage": 0.10,  # tỷ lệ dữ liệu dành cho đánh giá
  "min_segment_duration": 0.33,  # độ dài tối thiểu của mỗi đoạn âm thanh (giây)
  "max_text_length": 250,  # độ dài tối đa của text_content
  "target_text_length": 200  # độ dài mục tiêu khi chia nhỏ text_content
}


def check_text_file_exists(audio_path: str) -> Tuple[bool, Optional[str]]:
  """
  Kiểm tra xem có file text tương ứng với file audio không.

  Args:
      audio_path: Đường dẫn đến file âm thanh

  Returns:
      Tuple[bool, Optional[str]]: (Có tồn tại file text, Nội dung file text)
  """
  try:
    # Lấy tên file không có phần mở rộng
    audio_file_name, _ = os.path.splitext(audio_path)
    text_file_path = f"{audio_file_name}.txt"

    # Kiểm tra xem file text có tồn tại không
    if os.path.exists(text_file_path):
      # Đọc nội dung file text
      with open(text_file_path, 'r', encoding='utf-8') as f:
        text_content = f.read().strip()

      if text_content:  # Kiểm tra nội dung không rỗng
        logger.info(f"Đã tìm thấy file text tương ứng: {text_file_path}")
        return True, text_content
      else:
        logger.warning(f"File text tồn tại nhưng rỗng: {text_file_path}")
        return False, None
    else:
      logger.info(f"Không tìm thấy file text tương ứng cho {audio_path}")
      return False, None
  except Exception as e:
    logger.error(f"Lỗi khi kiểm tra file text: {str(e)}")
    return False, None


def get_audio_duration(audio_path: str) -> float:
  """
  Lấy thời lượng của file âm thanh sử dụng ffmpeg trực tiếp.

  Args:
      audio_path: Đường dẫn đến file âm thanh

  Returns:
      float: Thời lượng của file âm thanh (giây)
  """
  try:
    # Sử dụng ffprobe trực tiếp thông qua subprocess
    command = ["ffprobe", "-v", "error", "-show_entries", "format=duration",
               "-of", "default=noprint_wrappers=1:nokey=1", audio_path]
    output = subprocess.check_output(command,
                                     stderr=subprocess.STDOUT).decode().strip()
    duration = float(output)
    return duration
  except Exception as e:
    logger.error(f"Lỗi khi lấy thời lượng âm thanh: {str(e)}")
    return 0.0


def split_long_text(text: str,
    max_length: int = DEFAULT_CONFIG["max_text_length"],
    target_length: int = DEFAULT_CONFIG["target_text_length"]) -> List[str]:
  """
  Chia nhỏ văn bản dài thành các đoạn nhỏ hơn dựa trên các dấu ngắt câu.

  Args:
      text: Văn bản cần chia nhỏ
      max_length: Độ dài tối đa cho mỗi đoạn
      target_length: Độ dài mục tiêu cho mỗi đoạn

  Returns:
      List[str]: Danh sách các đoạn văn bản đã chia nhỏ
  """
  # Nếu văn bản đủ ngắn, trả về nguyên văn
  if len(text) <= max_length:
    return [text]

  # Các dấu ngắt câu theo thứ tự ưu tiên
  sentence_delimiters = ['. ', '! ', '? ', '; ', '\n', ', ']

  # Kết quả
  segments = []
  remaining_text = text

  while len(remaining_text) > max_length:
    # Tìm vị trí cắt phù hợp
    cut_position = -1

    # Thử từng loại dấu ngắt câu
    for delimiter in sentence_delimiters:
      # Tìm vị trí dấu ngắt câu gần nhất trong khoảng target_length đến max_length
      pos = -1
      for match in re.finditer(re.escape(delimiter), remaining_text):
        if target_length <= match.start() <= max_length:
          pos = match.start() + len(delimiter)
          break

      if pos > 0:
        cut_position = pos
        break

    # Nếu không tìm thấy dấu ngắt câu phù hợp, cắt tại vị trí max_length
    if cut_position == -1:
      cut_position = max_length

    # Cắt văn bản và thêm vào kết quả
    segment = remaining_text[:cut_position].strip()
    if segment:
      segments.append(segment)

    # Cập nhật văn bản còn lại
    remaining_text = remaining_text[cut_position:].strip()

  # Thêm phần còn lại vào kết quả
  if remaining_text:
    segments.append(remaining_text)

  return segments


def process_single_audio_file(
    audio_path: str,
    out_path: str,
    target_language: str,
    speaker_name: str,
    buffer: float,
    metadata: Dict[str, List] = None
) -> Tuple[int, float, int]:
  """
  Xử lý một file âm thanh và thêm vào metadata.

  Args:
      audio_path: Đường dẫn đến file âm thanh
      out_path: Đường dẫn đầu ra
      target_language: Ngôn ngữ của dữ liệu
      speaker_name: Tên của người nói
      buffer: Khoảng đệm thời gian (giây)
      metadata: Dictionary chứa metadata

  Returns:
      Tuple[int, float, int]: (Số đoạn đã xử lý, Thời lượng âm thanh, Trạng thái: 1=thành công, 0=thất bại)
  """
  try:
    # Kiểm tra file có tồn tại không
    if not os.path.exists(audio_path):
      logger.error(f"File không tồn tại: {os.path.basename(audio_path)}")
      return 0, 0, 0

    # Kiểm tra định dạng file
    file_ext = os.path.splitext(audio_path)[1].lower()
    if file_ext not in [".wav", ".mp3", ".flac"]:
      logger.warning(
        f"File không được hỗ trợ: {os.path.basename(audio_path)} (định dạng {file_ext})")
      return 0, 0, 0

    # Tải file âm thanh
    try:
      wav, sr = torchaudio.load(audio_path)
      logger.info(
        f"Tải âm thanh thành công: shape={wav.shape}, sample_rate={sr}")
    except Exception as e:
      logger.error(
        f"Không thể tải file âm thanh {os.path.basename(audio_path)}: {str(e)}")
      return 0, 0, 0

    # # Chuyển đổi stereo sang mono nếu cần (Thử comment out để so sánh)
    # if wav.size(0) != 1:
    #   wav = torch.mean(wav, dim=0, keepdim=True)

    wav = wav.squeeze()
    file_duration = wav.size(-1) / sr

    # Kiểm tra âm thanh quá ngắn
    if file_duration < 0.3:  # Âm thanh dưới 0.3 giây
      # Sử dụng logger mới
      logger.warning(
          f"File âm thanh quá ngắn: {os.path.basename(audio_path)} ({file_duration:.2f} giây)",
          {"file": audio_path, "duration": file_duration}
      )
      return 0, 0, 0

    # Kiểm tra xem có file text tương ứng không
    has_text_file, text_content = check_text_file_exists(audio_path)

    segments = []

    if has_text_file:
      # Sử dụng nội dung từ file text
      logger.info(f"Sử dụng nội dung từ file text cho {audio_path}")

      # Chuẩn hóa câu
      try:
        text_content = multilingual_cleaners(text_content, target_language)
      except Exception as e:
        logger.warning(f"Lỗi khi chuẩn hóa câu: {str(e)}. Sử dụng câu gốc.")

      # Kiểm tra độ dài của text_content
      if len(text_content) > DEFAULT_CONFIG["max_text_length"]:
        # Chia nhỏ văn bản
        text_segments = split_long_text(text_content)

        # Tính toán thời lượng cho mỗi đoạn dựa trên tỷ lệ
        segment_durations = []
        total_text_length = sum(len(seg) for seg in text_segments)

        for seg in text_segments:
          # Tính tỷ lệ thời lượng dựa trên độ dài văn bản
          duration_ratio = len(seg) / total_text_length
          segment_duration = file_duration * duration_ratio
          segment_durations.append(segment_duration)

        # Tạo các segment với thời lượng tương ứng
        current_start = 0
        for i, (seg, duration) in enumerate(
            zip(text_segments, segment_durations)):
          segments.append({
            "text": seg,
            "start": current_start,
            "end": current_start + duration
          })
          current_start += duration
      else:
        # Tạo một segment duy nhất cho toàn bộ file
        segments.append({
          "text": text_content,
          "start": 0,
          "end": file_duration
        })
    else:
      logger.info(f"Trích xuất văn bản từ âm thanh bằng Whisper: {audio_path}")

      try:
        # Sử dụng module ASR để trích xuất văn bản
        result = transcribe_audio(
            audio_path,
            language=target_language if target_language != "en" else None,
            word_timestamps=True
        )

        # Trích xuất segmentations
        segments = result["segments"]
        logger.info(f"Đã trích xuất {len(segments)} phân đoạn")

        # Kiểm tra và chia nhỏ các segment có văn bản quá dài
        new_segments = []
        for segment in segments:
          if len(segment["text"]) > DEFAULT_CONFIG["max_text_length"]:
            logger.info(
                f"Segment có văn bản quá dài ({len(segment['text'])} ký tự), chia nhỏ")

            # Chia nhỏ văn bản
            text_segments = split_long_text(segment["text"])

            # Tính toán thời lượng cho mỗi đoạn
            segment_duration = segment["end"] - segment["start"]
            sub_duration = segment_duration / len(text_segments)

            # Tạo các segment mới
            for i, text_seg in enumerate(text_segments):
              start_time = segment["start"] + i * sub_duration
              end_time = start_time + sub_duration

              new_segments.append({
                "text": text_seg,
                "start": start_time,
                "end": end_time
              })
          else:
            new_segments.append(segment)

        # Cập nhật danh sách segments
        segments = new_segments
        logger.info(f"Sau khi chia nhỏ: {len(segments)} phân đoạn")
      except Exception as e:
        error_msg = f"Lỗi khi dùng Whisper để trích xuất văn bản: {str(e)}"
        logger.error(error_msg, {
          "exception": str(e),
          "traceback": traceback.format_exc(),
          "file": audio_path
        })
        return 0, 0, 0

    # Tạo danh sách các phân đoạn theo câu
    segment_count = 0

    for segment in segments:
      # Lấy nội dung văn bản
      sentence = segment["text"].strip()
      if not sentence:
        continue

      # Lấy thời gian bắt đầu và kết thúc
      start_time = max(segment["start"] - buffer, 0)
      end_time = min(segment["end"] + buffer, wav.size(-1) / sr)

      # Chuẩn hóa câu nếu chưa được chuẩn hóa (trường hợp từ Whisper)
      if not has_text_file:
        try:
          sentence = multilingual_cleaners(sentence, target_language)
        except Exception as e:
          logger.warning(f"Lỗi khi chuẩn hóa câu: {str(e)}. Sử dụng câu gốc.")

      audio_file_name, _ = os.path.splitext(os.path.basename(audio_path))
      # Sử dụng định dạng tên file mới: tên_file_gốc + v + số thứ tự
      audio_file = f"wavs/{audio_file_name}v{str(segment_count).zfill(3)}.wav"

      absolute_path = os.path.join(out_path, audio_file)
      os.makedirs(os.path.dirname(absolute_path), exist_ok=True)
      segment_count += 1

      # Trích xuất đoạn âm thanh
      audio = wav[int(sr * start_time):int(sr * end_time)].unsqueeze(0)
      segment_duration = audio.size(-1) / sr

      # Nếu âm thanh quá ngắn thì bỏ qua (tức < 0.33 giây)
      if segment_duration >= DEFAULT_CONFIG["min_segment_duration"]:
        try:
          torchaudio.save(absolute_path, audio, sr)
          metadata["audio_file"].append(audio_file)
          metadata["text"].append(sentence)
          metadata["speaker_name"].append(speaker_name)
          metadata["duration"].append(
              round(segment_duration, 2))  # Làm tròn đến 2 chữ số thập phân
        except Exception as e:
          logger.error(f"Lỗi khi lưu đoạn âm thanh: {str(e)}")
          segment_count -= 1
      else:
        logger.warning(
            f"Bỏ qua đoạn âm thanh quá ngắn: {segment_duration:.2f} giây")
        segment_count -= 1

    logger.info(
        f"Đã xử lý xong file: {audio_path}, tạo được {segment_count} đoạn âm thanh")
    return segment_count, file_duration, 1

  except Exception as e:
    logger.error(f"Lỗi khi xử lý file {audio_path}: {str(e)}")
    logger.error(traceback.format_exc())
    print(json.dumps({"type": "error",
                      "message": f"Lỗi khi xử lý file {os.path.basename(audio_path)}: {str(e)}"}))
    return 0, 0, 0


def process_audio_files(
    audio_files: List[str],
    target_language: str = "en",
    out_path: Optional[str] = None,
    buffer: float = DEFAULT_CONFIG["buffer"],
    eval_percentage: float = DEFAULT_CONFIG["eval_percentage"],
    speaker_name: str = "custom_voice",
    process_id: Optional[str] = None
) -> Tuple[Optional[str], Optional[str], float]:
  try:
    # Kiểm tra đầu vào
    if not audio_files:
      logger.error("Không có file âm thanh nào được cung cấp")
      return None, None, 0

    logger.info(f"Bắt đầu xử lý {len(audio_files)} file âm thanh")

    # Tạo thư mục output nếu không tồn tại
    os.makedirs(out_path, exist_ok=True)
    wavs_path = os.path.join(out_path, "wavs")
    os.makedirs(wavs_path, exist_ok=True)

    # Tổng thời lượng âm thanh
    audio_total_size = 0

    metadata = {"audio_file": [], "text": [], "speaker_name": [],
                "duration": []}
    processed_files = 0
    failed_files = 0
    total_segments = 0

    # Xử lý từng file âm thanh
    for i, audio_path in enumerate(audio_files):
      # Báo cáo tiến trình
      progress = (i + 1) / len(audio_files) * 100
      logger.info(f"Xử lý file {i + 1}/{len(audio_files)}: {audio_path}", {
        "progress": progress,
        "current_file": os.path.basename(audio_path)
      })

      segments, duration, status = process_single_audio_file(
          audio_path=audio_path,
          out_path=out_path,
          target_language=target_language,
          speaker_name=speaker_name,
          buffer=buffer,
          metadata=metadata
      )

      if status == 1:
        processed_files += 1
        audio_total_size += duration
        total_segments += segments

        logger.data(
            "Xử lý file",
            {
              "processId": process_id,
              "fileName": os.path.basename(audio_path),
              "text": metadata["text"][-1] if metadata["text"] else "",
              "duration": round(duration, 2),
              "segments": segments,
              "totalFiles": processed_files,
              "totalSegments": total_segments,
              "totalDuration": audio_total_size,
              "progress": progress,
            },
            type="progress"
        )

      else:
        failed_files += 1
        logger.error(f"Xử lý thất bại: {os.path.basename(audio_path)}")

    # Báo cáo kết quả xử lý
    logger.info(
        f"Kết quả xử lý: {processed_files} file thành công, {failed_files} file thất bại, tạo được {total_segments} đoạn"
    )

    # Tạo DataFrame từ metadata
    df = pd.DataFrame(metadata)
    if len(df) == 0:
      logger.error("Không có dữ liệu nào được xử lý thành công!")
      return None, None, 0

    # Chia dữ liệu thành tập train và eval
    try:
      # Đặt seed cho việc xáo trộn để đảm bảo tính tái lập
      np.random.seed(42)
      df = df.sample(frac=1, random_state=42)

      # Điều chỉnh tỷ lệ eval dựa trên số lượng mẫu
      if len(df) < 20:
        adjusted_eval_percentage = 1.0 / len(df)
      elif len(df) < 100:
        adjusted_eval_percentage = 0.1
      else:
        adjusted_eval_percentage = eval_percentage

      num_val_samples = max(int(len(df) * adjusted_eval_percentage), 1)
      logger.info(
        f"Sử dụng {num_val_samples} mẫu cho tập đánh giá ({adjusted_eval_percentage * 100:.1f}%)")

      df_eval = df[:num_val_samples]
      df_train = df[num_val_samples:]

      # Xử lý text trước khi lưu vào CSV
      def sanitize_text_for_csv(text):
        if not isinstance(text, str):
          return str(text)
        text = text.replace('|', ' ')
        text = text.replace('\n', ' ')
        text = text.replace('\r', ' ')
        text = text.replace('"', '\"')
        return text

      df['text'] = df['text'].apply(sanitize_text_for_csv)

      # Lưu metadata
      df_train = df_train.sort_values('audio_file')
      train_metadata_path = os.path.join(out_path, "metadata_train.csv")
      df_train.to_csv(train_metadata_path, sep="|", index=False,
                      escapechar='\\')

      eval_metadata_path = os.path.join(out_path, "metadata_eval.csv")
      df_eval = df_eval.sort_values('audio_file')
      df_eval.to_csv(eval_metadata_path, sep="|", index=False, escapechar='\\')

      # Gửi thông báo hoàn thành
      logger.data(
          "Lưu metadata",
          {
            "processId": process_id,
            "trainPath": train_metadata_path,
            "trainCount": len(df_train),
            "evalPath": eval_metadata_path,
            "evalCount": len(df_eval),
            "totalDuration": audio_total_size,
            "totalFiles": len(df_train) + len(df_eval),
            "progress": 100
          },
          type="complete"
      )

      # Tạo bản sao dự phòng
      backup_dir = os.path.join(out_path, "backup")
      os.makedirs(backup_dir, exist_ok=True)
      df_train.to_csv(os.path.join(backup_dir, "metadata_train_backup.csv"),
                      sep="|", index=False)
      df_eval.to_csv(os.path.join(backup_dir, "metadata_eval_backup.csv"),
                     sep="|", index=False)

      # Ghi log thông tin kết quả
      logger.info(f"Tổng thời lượng âm thanh: {audio_total_size:.2f} giây")
      logger.info(f"Số mẫu huấn luyện: {len(df_train)}")
      logger.info(f"Số mẫu đánh giá: {len(df_eval)}")

      # Giải phóng bộ nhớ
      del df_train, df_eval, df, metadata
      gc.collect()

      return train_metadata_path, eval_metadata_path, audio_total_size

    except Exception as e:
      logger.error(f"Lỗi khi chia dữ liệu: {str(e)}", {
        "exception": str(e),
        "traceback": traceback.format_exc()
      })
      return None, None, 0

  except Exception as e:
    logger.error(f"Lỗi khi xử lý dữ liệu: {str(e)}", {
      "exception": str(e),
      "traceback": traceback.format_exc()
    })
    return None, None, 0


def main():
    import argparse
    parser = argparse.ArgumentParser(description='Process audio files and generate metadata')
    parser.add_argument('--input', required=True, help='Input folder containing audio files')
    parser.add_argument('--output', required=True, help='Output folder for processed files')
    parser.add_argument('--language', default='en', help='Target language')
    parser.add_argument('--speaker_name', required=True, help='Speaker name')
    parser.add_argument('--process_id', required=True, help='Process ID')
    
    args = parser.parse_args()
    
    try:
        # Lấy danh sách file audio từ thư mục input
        audio_files = []
        for file in os.listdir(args.input):
            if file.lower().endswith(('.wav', '.mp3', '.flac')):
                audio_files.append(os.path.join(args.input, file))
        
        if not audio_files:
            logger.error(f"Không tìm thấy file audio nào trong thư mục {args.input}")
            return
            
        # Xử lý các file audio
        train_path, eval_path, total_duration = process_audio_files(
            audio_files=audio_files,
            target_language=args.language,
            out_path=args.output,
            speaker_name=args.speaker_name,
            process_id=args.process_id
        )
        
        if train_path and eval_path:
            logger.info("Xử lý metadata hoàn tất", {
                "train_path": train_path,
                "eval_path": eval_path,
                "total_duration": total_duration
            })
        else:
            logger.error("Xử lý metadata thất bại")
            
    except Exception as e:
        logger.error(f"Lỗi khi xử lý metadata: {str(e)}")
        raise

if __name__ == '__main__':
    main()
