import json
import sys
from typing import Dict, List
import argparse

from logger import get_logger
from duration import get_audio_duration

# Khởi tạo logger
log = get_logger("tts_processor", "tts", min_level="debug")


def process_chunk(args: Dict) -> str | None:
  try:
    # Log input args
    print("args of process_chunk: ", args)
    log.data("Bắt đầu xử lý chunk", {
      "process_id": args.get("process_id"),
      "status": "running",
      "chunk_info": {
        "id": args.get('chunk_id'),
        "status": "running",
      }
    })

    # Chuyển args dict thành command line args
    cmd_args = []
    for key, value in args.items():
      if key == 'speaker_wav':
        # Xử lý đặc biệt cho speaker_wav - luôn truyền dưới dạng list
        if isinstance(value, list):
          cmd_args.extend([f'--{key}'] + value)
        else:
          # Nếu là string, t<PERSON>ch thành list
          cmd_args.extend(
              [f'--{key}'] + [wav.strip() for wav in value.split(',') if
                              wav.strip()])
      else:
        cmd_args.extend([f'--{key}', str(value)])

    # Import và chạy tts.py
    from tts import main as tts_main
    # Lưu sys.argv gốc
    original_argv = sys.argv
    try:
      # Set sys.argv mới
      sys.argv = ['tts.py'] + cmd_args
      # Chạy tts.py
      result = tts_main()
      if result == 0:
        return args['output']
      else:
        raise Exception("TTS process failed")
    finally:
      # Khôi phục sys.argv gốc
      sys.argv = original_argv

  except Exception as e:
    log.data("Lỗi khi xử lý chunk", {
      "process_id": args.get("process_id"),
      "status": "failed",
      "error": str(e),
      "chunk_info": {
        "id": f"{args.get('chunk_id')}",
        "status": "failed",
        "duration": 0.0,
        "error": str(e)
      }
    })
    # Re-raise exception để future.result() có thể catch được
    raise Exception(f"Chunk processing failed: {str(e)}")


def _parse_args_list_to_dict(args_list):
    """Chuyển một list các argument ['--key', 'value', ...] thành một dict."""
    args_dict = {}
    i = 0
    while i < len(args_list):
        key = args_list[i]
        if key.startswith('--'):
            # Xử lý các cờ có giá trị theo sau
            if i + 1 < len(args_list) and not args_list[i+1].startswith('--'):
                # Xử lý cờ đặc biệt 'speaker_wav' có thể có nhiều giá trị
                if key == '--speaker_wav':
                    if 'speaker_wav' not in args_dict:
                        args_dict['speaker_wav'] = []
                    args_dict['speaker_wav'].append(args_list[i+1])
                else:
                    args_dict[key.lstrip('-')] = args_list[i+1]
                i += 2
            else:
                # Xử lý các cờ không có giá trị (boolean flags), nếu có
                args_dict[key.lstrip('-')] = True
                i += 1
        else:
            # Bỏ qua các giá trị không hợp lệ
            i += 1
    return args_dict


def main():
  try:
    parser = argparse.ArgumentParser()
    parser.add_argument('processor_input_json', help='A JSON string containing chunks and args_list.')
    parser.add_argument('--process_id', help='The ID of the current process.')
    
    # parse_known_args để bỏ qua các args không xác định nếu có
    args, _ = parser.parse_known_args()

    process_id = args.process_id

    if process_id:
      log.set_process_id(process_id)

    # 1. Parse chuỗi JSON từ argument
    try:
        data = json.loads(args.processor_input_json)
    except json.JSONDecodeError as e:
        log.error(f"Lỗi khi parse input JSON: {e}", {"json_string": args.processor_input_json[:200]})
        return 1

    # 2. Lấy dữ liệu từ object đã parse
    chunk_infos: List[Dict] = data.get('chunks', [])
    args_list_from_json: List[List[str]] = data.get('args_list', [])

    if not chunk_infos or not args_list_from_json or len(chunk_infos) != len(args_list_from_json):
      log.error("Dữ liệu input không hợp lệ.", {"process_id": process_id})
      return 1
    total_chunks = len(chunk_infos)
    updatedChunks = []

    for i in range(total_chunks):
      chunk_info_obj = chunk_infos[i]
      args_data_as_list = args_list_from_json[i]
      
      chunk_id = chunk_info_obj.get("id")

      try:
        # 3. Chuyển đổi list args thành dict
        args_data_as_dict = _parse_args_list_to_dict(args_data_as_list)
        
        # 4. Tạo dict cuối cùng cho process_chunk
        # Không cần ghi đè 'text' và 'output' vì nó đã có trong args_data_as_dict
        current_args_for_chunk = {
            **args_data_as_dict,
            'process_id': process_id,
            'chunk_id': chunk_id
        }
      
        # 5. Gọi hàm xử lý
        output_path = process_chunk(current_args_for_chunk)
        if output_path is None:
            raise Exception(f"process_chunk for chunk {i} returned None")

        # 6. Xử lý kết quả và gửi log 
        duration = get_audio_duration(output_path)
        progress = int(((i + 1) / total_chunks) * 100)

        chunk_info = {
          "id": chunk_id,
          "status": "completed",
          "duration": duration
        }
        updatedChunks.append(chunk_info)
        
        log.data("Cập nhật tiến độ", {
            "process_id": process_id,
            "status": "running",
            "progress": progress,
            "chunk_info": chunk_info
        })
        
      except Exception as e:
        # Gửi thông báo lỗi cho chunk cụ thể này
        chunk_info = {
          "id": chunk_id,
          "status": "failed",
          "duration": 0,
          "error": str(e),
        }
        updatedChunks.append(chunk_info)
        
        # Nếu là chunk đầu tiên thì dừng ngay
        if i == 0:
          log.data("Lỗi chunk", {
            "process_id": process_id,
            "status": "failed",
            "chunk_info": chunk_info
          })
          return 1
        # Nếu không phải chunk đầu tiên thì tiếp tục vòng lặp
        log.data("Cập nhật tiến độ", {
            "process_id": process_id,
            "status": "running",
            "chunk_info": chunk_info
        })
        continue

    # Kiểm tra xem có chunk nào chưa completed không
    if any(chunk.get("status") != "completed" for chunk in updatedChunks):
      # Nếu có chunk chưa completed, gửi thông báo lỗi cho toàn bộ process
      log.data("Process thất bại do có chunk chưa hoàn thành", {
          "process_id": process_id,
          "status": "failed",
          "error": "Một số chunk chưa được xử lý thành công"
      })
      return 1

    # Gửi thông báo hoàn thành tất cả
    log.data("Tất cả chunk đã hoàn thành", {
        "process_id": process_id,
        "status": "running",
        "progress": 99,
    })
    return 0

  except Exception as e:
    log.error("Error in main", {"error": str(e), "process_id": process_id})
    return 1


if __name__ == "__main__":
  main()
