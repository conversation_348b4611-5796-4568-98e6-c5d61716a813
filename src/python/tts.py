#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module xử lý text-to-speech (TTS) sử dụng XTTS v2.
Hỗ trợ nhiều ngôn ngữ và sử dụng các voice đã fine-tune.
"""

import os
import sys
import time
import traceback
import uuid
from typing import List, Optional, Any

# Set environment variable to auto-accept ToS
os.environ["TTS_ACCEPT_TOS"] = "1"

import torch
import torchaudio
from TTS.tts.configs.xtts_config import XttsConfig
from TTS.tts.models.xtts import Xtts

from device_manager import get_device_manager
from logger import get_logger

# Khởi tạo logger
log = get_logger("tts", "tts", min_level="debug")

# Patch cho PyTorch 2.6+
try:
  torch_version = torch.__version__
  if torch_version.startswith("2.") and float(torch_version.split(".")[1]) >= 6:
    log.debug(
      f"<PERSON><PERSON><PERSON> hiện PyTorch {torch_version}, áp dụng patch cho torch.load")

    # Thêm các global an toàn
    torch.serialization.add_safe_globals([XttsConfig])

    # Patch torch.load
    original_load = torch.load


    def patched_load(f, map_location=None, pickle_module=None,
        **pickle_load_args):
      # Đặt weights_only=False để tương thích với các mô hình cũ
      pickle_load_args["weights_only"] = False
      return original_load(f, map_location, pickle_module, **pickle_load_args)


    # Thay thế hàm torch.load
    torch.load = patched_load
    log.debug("Đã patch torch.load để tương thích với PyTorch 2.6+")

except Exception as e:
  log.warning(f"Không thể patch torch.load: {e}")


def init_model(model_path: str, num_threads: int,
    config_path: Optional[str] = None, vocab_path: Optional[str] = None):
  """
  Khởi tạo model TTS

  Args:
      model_path: Đường dẫn đến model
      num_threads: Số lượng thread
      config_path: Đường dẫn đến config (None nếu là model builtin)
      vocab_path: Đường dẫn đến vocab (None nếu là model builtin)
  """
  try:
    # Lấy thiết bị hiện tại
    device_manager = get_device_manager()
    device = device_manager.get_current_device()

    # Cấu hình số thread phù hợp cho thiết bị
    torch.set_num_threads(max(1, num_threads))

    if config_path and vocab_path:
      # Khởi tạo model custom
      model_config = XttsConfig()
      model_config.load_json(config_path)
      model = Xtts.init_from_config(model_config, trust_remote_code=True)

      # Tải checkpoint
      model.load_checkpoint(
          config=model_config,
          checkpoint_path=model_path,
          checkpoint_dir=os.path.dirname(model_path),
          vocab_path=vocab_path,
          eval=True
      )
    else:
      # Khởi tạo model builtin
      from TTS.api import TTS
      model = TTS(model_name=model_path)
      log.info("Khởi tạo model builtin", {
        "model_name": model_path
      })

    # Chuyển model sang thiết bị
    model.to(device)
    return model
  except Exception as e:
    log.error(f"Lỗi khi khởi tạo model: {str(e)}")
    log.error(traceback.format_exc())
    return None


def synthesize(
    model: Any,
    text: str,
    language: str,
    speaker_wav: List[str],
    output_path: str,
    speed: float,
    temperature: float,
    repetition_penalty: float,
    top_k: int,
    top_p: float,
    length_penalty: float,
    process_id: Optional[str] = None,
    chunk_id: Optional[str] = None
) -> Optional[str]:
  """
  Tổng hợp giọng nói từ văn bản.

  Args:
      model: Model TTS đã được khởi tạo
      text: Văn bản cần chuyển thành giọng nói
      language: Ngôn ngữ của văn bản
      speaker_wav: Danh sách đường dẫn đến file âm thanh của người nói
      output_path: Đường dẫn đến file âm thanh đầu ra
      speed: Tốc độ nói
      temperature: Nhiệt độ cho quá trình sinh âm thanh
      repetition_penalty: Hệ số phạt lặp lại
      top_k: Số lượng token có xác suất cao nhất để chọn
      top_p: Ngưỡng xác suất tích lũy để chọn token
      length_penalty: Hệ số điều chỉnh độ dài âm thanh
      process_id: ID của quá trình
      chunk_id: ID của chunk đang xử lý

  Returns:
      Optional[str]: Đường dẫn đến file âm thanh đã tạo, hoặc None nếu thất bại
  """
  try:
    # Tổng hợp giọng nói
    log.info("Đang tổng hợp giọng nói", {
      "process_id": process_id,
      "chunk_id": chunk_id,
      "status": "running",
    })

    # Kiểm tra loại model và xử lý tương ứng
    if hasattr(model, 'tts_to_file'):
      # Xử lý model builtin
      # Kiểm tra xem model có phải là model đa ngôn ngữ không
      is_multilingual = 'multilingual' in getattr(model, 'model_name', '')

      # Chuẩn bị tham số cho tts_to_file
      tts_params = {
        "text": text,
        "speaker_wav": speaker_wav,
        "file_path": output_path,
        "speed": speed,
        "temperature": temperature,
        "repetition_penalty": repetition_penalty,
        "top_k": top_k,
        "top_p": top_p
      }
      
      # Chỉ thêm language nếu model hỗ trợ đa ngôn ngữ
      if is_multilingual:
        tts_params["language"] = language

      # Gọi tts_to_file với các tham số đã chuẩn bị
      model.tts_to_file(**tts_params)
    else:
      # Xử lý model trained
      gpt_cond_latent, speaker_embedding = model.get_conditioning_latents(
        audio_path=speaker_wav,
        gpt_cond_len=model.config.gpt_cond_len,
        max_ref_length=model.config.max_ref_len,
        sound_norm_refs=model.config.sound_norm_refs
      )

      # Chuẩn bị tham số cho inference
      inference_params = {
        "text": text,
        "gpt_cond_latent": gpt_cond_latent,
        "speaker_embedding": speaker_embedding,
        "temperature": temperature,
        "length_penalty": length_penalty,
        "repetition_penalty": repetition_penalty,
        "top_k": top_k,
        "top_p": top_p,
        "speed": speed,
        "language": language
      }

      wav = model.inference(**inference_params)["wav"]

      # Detach tensor trước khi lưu
      if isinstance(wav, torch.Tensor):
        wav = wav.detach().cpu()

      # Lưu file âm thanh
      log.info("Đang lưu file âm thanh", {
        "process_id": process_id,
        "chunk_id": chunk_id,
        "status": "running",
      })
      sample_rate = 24000
      if isinstance(wav, torch.Tensor):
        wav_tensor = wav.cpu().unsqueeze(0) if wav.dim() == 1 else wav.cpu()
      else:
        wav_tensor = torch.tensor(wav).unsqueeze(0)

      torchaudio.save(output_path, wav_tensor, sample_rate)

    # Gửi thông báo hoàn thành
    log.info("Đã tổng hợp giọng nói thành công", {
      "process_id": process_id,
      "chunk_id": chunk_id,
      "output_path": output_path
    })

    return output_path
  except Exception as e:
    log.data("Lỗi khi tổng hợp giọng nói", {
      "process_id": process_id,
      "chunk_id": chunk_id,
      "status": "failed",
      "error": str(e)
    })
    return None


def main():
  """Hàm chính xử lý tham số dòng lệnh và thực hiện TTS."""
  import argparse

  parser = argparse.ArgumentParser(description='Text-to-Speech sử dụng TTS')

  # Tham số bắt buộc
  parser.add_argument('--text', type=str, required=True,
                      help='Văn bản cần chuyển thành giọng nói')
  parser.add_argument('--model_path', type=str, required=True,
                      help='Đường dẫn đến model hoặc tên model builtin')
  parser.add_argument('--speaker_wav', type=str, nargs='+', required=True,
                      help='Đường dẫn đến file âm thanh của người nói (có thể chỉ định nhiều file)')

  # Tham số cho model custom
  parser.add_argument('--config_path', type=str,
                      help='Đường dẫn đến file config.json (chỉ dùng cho model custom)')
  parser.add_argument('--vocab_path', type=str,
                      help='Đường dẫn đến file vocab.json (chỉ dùng cho model custom)')

  # Tham số có default
  parser.add_argument('--language', type=str, default='en',
                      help='Ngôn ngữ của văn bản (mặc định: en)')
  parser.add_argument('--speed', type=float, default=1.0,
                      help='Tốc độ nói (mặc định: 1.0)')
  parser.add_argument('--temperature', type=float, default=0.7,
                      help='Nhiệt độ cho quá trình sinh âm thanh (0.0-1.0)')
  parser.add_argument('--repetition_penalty', type=float, default=2.0,
                      help='Hệ số phạt lặp lại (1.0-3.0)')
  parser.add_argument('--top_k', type=int, default=50,
                      help='Số lượng token có xác suất cao nhất để chọn (1-100)')
  parser.add_argument('--top_p', type=float, default=0.85,
                      help='Ngưỡng xác suất tích lũy để chọn token (0.0-1.0)')
  parser.add_argument('--length_penalty', type=float, default=1.0,
                      help='Hệ số điều chỉnh độ dài âm thanh (0.0-2.0)')
  parser.add_argument('--num_threads', type=int, default=1,
                      help='Số lượng thread (mặc định: 1)')
  parser.add_argument('--process_id', type=str, default=None,
                      help='ID của quá trình (mặc định: None)')
  parser.add_argument('--chunk_id', type=str, default=None,
                      help='ID của chunk đang xử lý (mặc định: None)')

  # Tham số output
  parser.add_argument('--output', type=str,
                      default=os.path.join('output',
                                           f'tts_{int(time.time())}_{uuid.uuid4()}.wav'),
                      help='Đường dẫn đến file âm thanh đầu ra')

  args = parser.parse_args()

  log.set_process_id(args.process_id)

  # Kiểm tra và tạo thư mục output
  output_dir = os.path.dirname(args.output)
  if not os.path.exists(output_dir):
    os.makedirs(output_dir)

  # Khởi tạo model
  model = init_model(args.model_path, args.num_threads, args.config_path,
                     args.vocab_path)
  if not model:
    log.error("Không thể khởi tạo model")
    return 1

  # Tổng hợp giọng nói
  result = synthesize(
      model=model,
      text=args.text,
      language=args.language,
      speaker_wav=args.speaker_wav,
      output_path=args.output,
      speed=args.speed,
      temperature=args.temperature,
      repetition_penalty=args.repetition_penalty,
      top_k=args.top_k,
      top_p=args.top_p,
      length_penalty=args.length_penalty,
      process_id=args.process_id,
      chunk_id=args.chunk_id
  )

  return 0 if result else 1


if __name__ == "__main__":
  sys.exit(main())
