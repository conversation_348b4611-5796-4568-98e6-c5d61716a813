#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module chứa các tiện ích xử lý văn bản cho TTS.
"""

import argparse
import json
import os
import re

from TTS.tts.layers.xtts.tokenizer import multilingual_cleaners
from TTS.tts.utils.text.characters import Graphemes
from TTS.tts.utils.text.tokenizer import TTSTokenizer

from logger import get_logger

# Khởi tạo logger
logger = get_logger("text_utils", "tts")

# Khởi tạo tokenizer
tokenizer = TTSTokenizer(characters=Graphemes())


def count_tokens(text, language):
  """
  Đếm số lượng tokens trong văn bản sử dụng tokenizer của TTS.
  """
  logger.debug(f"Đếm tokens cho text: {text[:30]}...")

  # Làm sạch văn bản
  cleaned_text = multilingual_cleaners(text, language)
  logger.debug(f"Text sau khi làm sạch: {cleaned_text[:30]}...")

  # Sử dụng tokenizer
  tokens = tokenizer.text_to_ids(cleaned_text, language)
  token_count = len(tokens)
  logger.debug(f"Số lượng tokens: {token_count}")

  return token_count


def split_text_by_tokens(text, language, max_tokens):
  """
  Tách văn bản thành các đoạn nhỏ hơn dựa theo số lượng tokens.
  Đảm bảo mỗi chunk có ít nhất 100 tokens và không vượt quá max_tokens.
  Sử dụng thuật toán thông minh để gộp các câu lại với nhau.
  """
  logger.info(f"Bắt đầu tách text với max_tokens={max_tokens}")

  # Nếu văn bản ngắn, không cần tách
  total_tokens = count_tokens(text, language)
  logger.info(f"Tổng số tokens: {total_tokens}")

  if total_tokens <= max_tokens:
    logger.info("Text ngắn, không cần tách")
    return [text]

  # Tách văn bản theo câu
  pattern = r'([.!?;。？！])\s*'
  sentences = [s.strip() for s in re.sub(pattern, r'\1\n', text).split('\n') if s.strip()]
  logger.info(f"Số câu: {len(sentences)}")

  # Tính số tokens cho từng câu
  sentence_tokens = [(s, count_tokens(s, language)) for s in sentences]
  logger.debug(f"Tokens của từng câu: {sentence_tokens}")

  # Thuật toán gộp câu thông minh
  chunks = []
  current_chunk = ""
  current_tokens = 0
  min_tokens = 100  # Số tokens tối thiểu cho mỗi chunk

  for sentence, tokens in sentence_tokens:
    # Nếu câu đơn lẻ vượt quá max_tokens, tách nhỏ theo dấu phẩy
    if tokens > max_tokens:
      if current_chunk:
        chunks.append(current_chunk)
        current_chunk = ""
        current_tokens = 0
      
      # Tách câu dài thành các phần nhỏ hơn theo dấu phẩy
      parts = [p.strip() for p in sentence.split(',') if p.strip()]
      temp_chunk = ""
      temp_tokens = 0
      
      for part in parts:
        part_tokens = count_tokens(part, language)
        if temp_tokens + part_tokens <= max_tokens:
          temp_chunk += (", " if temp_chunk else "") + part
          temp_tokens += part_tokens
        else:
          if temp_chunk:
            chunks.append(temp_chunk)
          temp_chunk = part
          temp_tokens = part_tokens
      
      if temp_chunk:
        chunks.append(temp_chunk)
      continue

    # Nếu thêm câu mới vẫn trong giới hạn max_tokens
    if current_tokens + tokens <= max_tokens:
      current_chunk += (" " if current_chunk else "") + sentence
      current_tokens += tokens
    else:
      # Nếu chunk hiện tại đã đủ dài (>= min_tokens), tạo chunk mới
      if current_tokens >= min_tokens:
        chunks.append(current_chunk)
        current_chunk = sentence
        current_tokens = tokens
      else:
        # Nếu chunk hiện tại quá ngắn, thử gộp với câu tiếp theo
        # Kiểm tra xem có thể gộp với câu tiếp theo không
        next_sentence_idx = sentence_tokens.index((sentence, tokens)) + 1
        if next_sentence_idx < len(sentence_tokens):
          next_sentence, next_tokens = sentence_tokens[next_sentence_idx]
          if current_tokens + tokens + next_tokens <= max_tokens:
            current_chunk += (" " if current_chunk else "") + sentence
            current_tokens += tokens
          else:
            chunks.append(current_chunk)
            current_chunk = sentence
            current_tokens = tokens
        else:
          # Nếu là câu cuối cùng, gộp vào chunk hiện tại
          current_chunk += (" " if current_chunk else "") + sentence
          current_tokens += tokens

  # Thêm chunk cuối cùng nếu có
  if current_chunk:
    chunks.append(current_chunk)

  logger.info(f"Tách text thành {len(chunks)} chunks")
  return chunks


def main():
  parser = argparse.ArgumentParser(description='Split text into chunks')
  parser.add_argument('--text', required=True, help='Input text to split')
  parser.add_argument('--output', required=True, help='Output JSON file path')
  parser.add_argument('--language', required=True, help='Language of the text')
  parser.add_argument('--max_tokens', type=int, default=240,
                      help='Maximum tokens per chunk')
  parser.add_argument('--process_id', type=str, help='Process ID for logging')

  known_args, unknown_args = parser.parse_known_args() 

  if unknown_args:
        print(f"TEXT_UTILS: Bỏ qua các tham số không xác định: {unknown_args}")


  # Set process_id cho logger nếu có
  if known_args.process_id:
    logger.set_process_id(known_args.process_id)

  # Parse text từ JSON string
  try:
    text = json.loads(known_args.text)
    logger.info("Đã parse text từ JSON string", {"process_id": known_args.process_id})
  except json.JSONDecodeError:
    text = known_args.text
    logger.info("Sử dụng text gốc", {"process_id": known_args.process_id})

  logger.info(f"Bắt đầu xử lý text với ngôn ngữ: {known_args.language}",
              {"process_id": known_args.process_id})
  chunks = split_text_by_tokens(text, known_args.language, known_args.max_tokens)

  # Ghi kết quả vào file JSON
  logger.info(f"Ghi kết quả vào file: {known_args.output}",
              {"process_id": known_args.process_id})
  try:
    with open(known_args.output, 'w', encoding='utf-8') as f:
      json.dump(chunks, f, ensure_ascii=False)
      f.flush()  # Đẩy dữ liệu ra file
      os.fsync(f.fileno())  # Đảm bảo dữ liệu được ghi xuống đĩa
    logger.info("Ghi file thành công", {"process_id": known_args.process_id})
  except Exception as e:
    logger.error(f"Lỗi khi ghi file: {str(e)}", {"process_id": known_args.process_id})
    raise


if __name__ == '__main__':
  main()
