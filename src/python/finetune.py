#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import os
import platform
import shutil
import sys
import time
import traceback
from typing import Tuple, Optional

import pandas as pd

from logger import setup_logging

# Thiết lập logger cho module này
log = setup_logging(channel="finetune", min_level="debug")

# Import các thư viện đã kiểm tra
import torch
import torchaudio
from TTS.utils.manage import ModelManager

# Import device_manager để quản lý thiết bị
from device_manager import get_device_manager, check_memory, check_disk_space, \
  clear_gpu_cache, \
  move_model_to_device, execute_with_best_device_and_setup

# Tự động đồng ý với điều khoản sử dụng của Coqui
os.environ['COQUI_TOS_AGREED'] = 'y'

# Cấu hình môi trường
ENV = os.getenv("ENVIRONMENT", "development").lower()
DEBUG = ENV == "development" or os.getenv("DEBUG", "false").lower() == "true"

# Thiết lập cấu hình mặc định
DEFAULT_CONFIG = {
  "whisper_model": "tiny",  # tiny, base, small, medium, large
  "max_audio_length": 30,  # giới hạn độ dài âm thanh (giây)
  "eval_percentage": 0.10,  # tỷ lệ dữ liệu dành cho đánh giá
  "buffer": 0.2,  # khoảng đệm thời gian (giây)
  "min_audio_length": 3,  # độ dài tối thiểu của dữ liệu âm thanh (giây)
  "recommended_audio_length": 60,
  'learning_rate': 5e-6,
  "grad_acumm": 4,  # số bước tích lũy gradient
  "test_text": {
    "en": "Hello, this is my voice after being fine-tuned.",
    "vi": "Xin chào, đây là giọng nói của tôi sau khi được fine-tune."
  }
}

def train_xtts_model(
    language: str,
    train_csv: str,
    eval_csv: str,
    num_epochs: int = 20,
    batch_size: int = 10,
    grad_acumm: int = DEFAULT_CONFIG["grad_acumm"],
    output_path: Optional[str] = None,
    max_audio_length: int = DEFAULT_CONFIG["max_audio_length"],
    voice_name: str = "custom_voice") -> Tuple[
  Optional[str], Optional[str], Optional[str], Optional[str]
]:
  """
  Huấn luyện mô hình XTTS.

  Args:
      language: Ngôn ngữ của dữ liệu
      train_csv: Đường dẫn đến file CSV chứa dữ liệu train
      eval_csv: Đường dẫn đến file CSV chứa dữ liệu đánh giá
      num_epochs: Số epoch huấn luyện (mặc định: 10)
      batch_size: Kích thước batch (mặc định: 4)
      grad_acumm: Số bước tích lũy gradient (mặc định: 1)
      output_path: Đường dẫn đầu ra
      max_audio_length: Độ dài tối đa của âm thanh (giây)
      voice_name: Tên của giọng nói

  Returns:
      Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
          config_path, vocab_file, ft_xtts_checkpoint, speaker_wav
  """
  try:
    # Kiểm tra các file đầu vào
    if not os.path.exists(train_csv):
      log.error(f"File train không tồn tại: {train_csv}")
      print(json.dumps({"type": "error",
                        "message": f"File train không tồn tại: {train_csv}"}))
      return None, None, None, None

    if not os.path.exists(eval_csv):
      log.error(f"File eval không tồn tại: {eval_csv}")
      print(json.dumps(
          {"type": "error", "message": f"File eval không tồn tại: {eval_csv}"}))
      return None, None, None, None

    # Kiểm tra dung lượng ổ cứng
    if not check_disk_space(output_path, required_gb=15.0):
      print(json.dumps({"type": "warning",
                        "message": "Dung lượng ổ cứng thấp, có thể gặp vấn đề trong quá trình huấn luyện"}))

    # Kiểm tra bộ nhớ
    try:
      import psutil
      memory_info = check_memory()
      if memory_info["system_available_gb"] < 4.0:
        log.warning(
            f"Bộ nhớ hệ thống thấp: {memory_info['system_available_gb']:.2f} GB. Khuyến nghị ít nhất 8GB RAM trống.")
        print(json.dumps({"type": "warning",
                          "message": f"Bộ nhớ hệ thống thấp: {memory_info['system_available_gb']:.2f} GB. Khuyến nghị ít nhất 8GB RAM trống."}))
    except ImportError:
      pass

    # Không cần import train_gpt ở đây nữa, sẽ import trong khối try bên dưới

    log.info("Bắt đầu huấn luyện mô hình XTTS")
    log.info(f"Ngôn ngữ: {language}")
    log.info(f"File train: {train_csv}")
    log.info(f"File eval: {eval_csv}")
    log.info(f"Số epoch: {num_epochs}")
    log.info(f"Batch size: {batch_size}")
    log.info(f"Gradient accumulation: {grad_acumm}")

    print(json.dumps(
        {"type": "info", "message": "Bắt đầu huấn luyện mô hình XTTS"}))
    print(json.dumps({"type": "info",
                      "message": f"Ngôn ngữ: {language}, Số epoch: {num_epochs}, Batch size: {batch_size}"}))

    # Đảm bảo mô hình XTTS đã được tải xuống
    log.info("Đảm bảo mô hình XTTS đã được tải xuống...")
    print(
        json.dumps({"type": "info", "message": "Đang tải mô hình XTTS gốc..."}))

    try:
      model_manager = ModelManager()
      model_path, _, _ = model_manager.download_model(
          'tts_models/multilingual/multi-dataset/xtts_v2')
      log.info(f"Sử dụng mô hình XTTS từ đường dẫn: {model_path}")
    except Exception as e:
      log.error(f"Không thể tải mô hình XTTS gốc: {str(e)}")
      print(json.dumps({"type": "error",
                        "message": f"Không thể tải mô hình XTTS gốc: {str(e)}"}))
      return None, None, None, None

    # Chuyển đổi giây thành khung sóng
    max_audio_length_samples = int(max_audio_length * 22050)

    # Patch torch.load nếu cần (cho PyTorch >= 2.6)
    try:
      torch_version = torch.__version__
      if torch_version.startswith("2.") and float(
          torch_version.split(".")[1]) >= 6:
        original_load = torch.load

        def patched_load(f, map_location=None, pickle_module=None,
            **pickle_load_args):
          pickle_load_args["weights_only"] = False
          return original_load(f, map_location, pickle_module,
                               **pickle_load_args)

        torch.load = patched_load
        log.info("Đã patch torch.load")

        # Thêm XttsConfig vào safe_globals
        try:
          from TTS.tts.configs.xtts_config import XttsConfig
          torch.serialization.add_safe_globals([XttsConfig])
        except (ImportError, AttributeError) as e:
          log.warning(
              f"Không thể thêm XttsConfig vào safe_globals: {str(e)}")
    except Exception as e:
      log.warning(f"Không thể patch torch.load: {str(e)}")

    log.info("Bắt đầu quá trình huấn luyện...")

    try:
      # Import train_gpt
      from TTS.demos.xtts_ft_demo.utils.gpt_train import train_gpt

      # Sử dụng trực tiếp execute_with_best_device_and_setup để thực thi train_gpt trên thiết bị tốt nhất
      config_path, original_xtts_checkpoint, vocab_file, exp_path, speaker_wav = execute_with_best_device_and_setup(
          train_gpt,
          language=language,
          num_epochs=num_epochs,
          batch_size=batch_size,
          grad_acumm=grad_acumm,
          train_csv=train_csv,
          eval_csv=eval_csv,
          output_path=output_path,
          max_audio_length=max_audio_length_samples,
      )
    except Exception as e:
      log.error(f"Lỗi trong quá trình huấn luyện: {str(e)}")
      log.error(traceback.format_exc())
      return None, None, None, None

    # Sao chép các file gốc để tránh các vấn đề về thay đổi tham số
    log.info(f"Sao chép {config_path} và {vocab_file} vào {exp_path}")
    try:
      shutil.copy(config_path, exp_path)
      shutil.copy(vocab_file, exp_path)
    except Exception as e:
      log.warning(f"Không thể sao chép file: {str(e)}")

    # Tìm checkpoint tốt nhất
    try:
      checkpoint_files = [f for f in os.listdir(exp_path) if
                          f.startswith("checkpoint_") and f.endswith(".pth")]
      if checkpoint_files:
        # Sắp xếp theo số bước và lấy file cuối cùng
        checkpoint_files.sort(key=lambda x: int(x.split("_")[1].split(".")[0]))
        best_checkpoint = os.path.join(exp_path, checkpoint_files[-1])

        # Sao chép checkpoint tốt nhất thành model.pth
        model_pth = os.path.join(exp_path, "model.pth")
        shutil.copy(best_checkpoint, model_pth)

        # Tạo thêm một bản sao với tên voice_name
        voice_model_path = os.path.join(output_path, f"{voice_name}.pth")
        shutil.copy(best_checkpoint, voice_model_path)

        ft_xtts_checkpoint = model_pth
        log.info(
          f"Sử dụng checkpoint: {best_checkpoint} (bước {checkpoint_files[-1].split('_')[1].split('.')[0]})")
      else:
        ft_xtts_checkpoint = os.path.join(exp_path, "best_model.pth")
        log.info(f"Không tìm thấy checkpoint, sử dụng: {ft_xtts_checkpoint}")
    except Exception as e:
      log.error(f"Lỗi khi xử lý checkpoint: {str(e)}")
      ft_xtts_checkpoint = original_xtts_checkpoint

    log.info(f"Model checkpoint: {os.path.basename(ft_xtts_checkpoint)}")

    # Tạo file metadata cho mô hình
    try:
      model_info = {
        "name": voice_name,
        "language": language,
        "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "num_epochs": num_epochs,
        "batch_size": batch_size,
        "model_path": ft_xtts_checkpoint,
        "config_path": config_path,
        "vocab_path": vocab_file,
        "speaker_wav": speaker_wav
      }

      model_info_path = os.path.join(exp_path, "model_info.json")
      with open(model_info_path, 'w', encoding='utf-8') as f:
        json.dump(model_info, f, indent=2)

      log.info(f"Lưu thông tin mô hình tại: {model_info_path}")
    except Exception as e:
      log.warning(f"Không thể lưu thông tin mô hình: {str(e)}")

    # Xóa cache GPU
    clear_gpu_cache()

    log.info("Hoàn thành huấn luyện mô hình XTTS")

    return config_path, vocab_file, ft_xtts_checkpoint, speaker_wav

  except Exception as e:
    log.error(f"Lỗi khi huấn luyện mô hình: {str(e)}")
    log.error(traceback.format_exc())
    return None, None, None, None


def test_tts_model(checkpoint_path: str, config_path: str, vocab_path: str,
    text: str,
    language: str = "en", reference_audio: Optional[str] = None,
    output_path: Optional[str] = None) -> Optional[
  str]:
  """
  Kiểm tra mô hình TTS đã huấn luyện.

  Args:
      checkpoint_path: Đường dẫn đến file checkpoint của mô hình
      config_path: Đường dẫn đến file cấu hình của mô hình
      vocab_path: Đường dẫn đến file từ vựng
      text: Văn bản đầu vào để tổng hợp giọng nói
      language: Ngôn ngữ của văn bản (mặc định: "en")
      reference_audio: Đường dẫn đến file âm thanh tham chiếu
      output_path: Đường dẫn đến thư mục để lưu file âm thanh đã tạo

  Returns:
      Optional[str]: Đường dẫn đến file âm thanh đã tạo hoặc None nếu có lỗi
  """
  try:
    # Kiểm tra các file đầu vào
    if not os.path.exists(checkpoint_path):
      log.error(f"File checkpoint không tồn tại: {checkpoint_path}")
      print(json.dumps({"type": "error",
                        "message": f"File checkpoint không tồn tại: {os.path.basename(checkpoint_path)}"}))
      return None

    if not os.path.exists(config_path):
      log.error(f"File config không tồn tại: {config_path}")
      print(json.dumps({"type": "error",
                        "message": f"File config không tồn tại: {os.path.basename(config_path)}"}))
      return None

    if not os.path.exists(vocab_path):
      log.error(f"File vocab không tồn tại: {vocab_path}")
      print(json.dumps({"type": "error",
                        "message": f"File vocab không tồn tại: {os.path.basename(vocab_path)}"}))
      return None

    # Import các module cần thiết
    try:
      from TTS.tts.configs.xtts_config import XttsConfig
      from TTS.tts.models.xtts import Xtts
    except ImportError as e:
      log.error(f"Không thể import các module cần thiết: {str(e)}")
      print(json.dumps({"type": "error",
                        "message": f"Không thể import các module cần thiết: {str(e)}"}))
      return None

    log.info("Bắt đầu kiểm tra mô hình TTS")
    print(
        json.dumps({"type": "info", "message": "Bắt đầu kiểm tra mô hình TTS"}))

    # Lấy thiết bị tốt nhất (thiết lập môi trường sẽ được thực hiện trong move_model_to_device)
    device_manager = get_device_manager()
    device = device_manager.get_current_device()

    # Tải mô hình
    log.info("Đang tải mô hình XTTS...")
    print(json.dumps(
        {"type": "info", "message": "Đang tải mô hình XTTS đã fine-tune..."}))

    try:
      config = XttsConfig()
      config.load_json(config_path)

      model = Xtts.init_from_config(config)
      # Lấy checkpoint_dir từ checkpoint_path để tránh lỗi None trong os.path.join
      checkpoint_dir = os.path.dirname(checkpoint_path)
      model.load_checkpoint(config, checkpoint_path=checkpoint_path,
                            checkpoint_dir=checkpoint_dir,
                            vocab_path=vocab_path, use_deepspeed=False)

      log.info("Đã tải mô hình XTTS thành công")
    except Exception as e:
      log.error(f"Lỗi khi tải mô hình: {str(e)}")
      log.error(traceback.format_exc())
      print(json.dumps(
          {"type": "error", "message": f"Lỗi khi tải mô hình: {str(e)}"}))
      return None

    # Đặt mô hình lên thiết bị phù hợp sử dụng device_manager
    model, device = move_model_to_device(model, device)

    print(
        json.dumps(
            {"type": "info", "message": "Đã tải mô hình XTTS thành công"}))

    # Nếu không có file âm thanh tham chiếu, sử dụng file đầu tiên từ metadata train
    if reference_audio is None:
      try:
        train_metadata_path = os.path.join(os.path.dirname(config_path),
                                           "metadata_train.csv")
        if os.path.exists(train_metadata_path):
          train_metadata = pd.read_csv(train_metadata_path, sep="|")
          if len(train_metadata) > 0:
            first_audio = train_metadata.iloc[0]["audio_file"]
            reference_audio = os.path.join(os.path.dirname(config_path),
                                           first_audio)
            log.info(f"Sử dụng file âm thanh tham chiếu: {reference_audio}")
          else:
            log.error("File metadata_train.csv không có dữ liệu")
            print(json.dumps({"type": "error",
                              "message": "File metadata_train.csv không có dữ liệu"}))
            return None
        else:
          log.error(
              f"Không tìm thấy file metadata_train.csv tại {os.path.dirname(config_path)}")
          print(json.dumps({"type": "error",
                            "message": f"Không tìm thấy file metadata_train.csv"}))
          return None
      except Exception as e:
        log.error(f"Lỗi khi tìm file âm thanh tham chiếu: {str(e)}")
        print(json.dumps({"type": "error",
                          "message": f"Lỗi khi tìm file âm thanh tham chiếu: {str(e)}"}))
        return None

    # Kiểm tra file âm thanh tham chiếu
    if not os.path.exists(reference_audio):
      log.error(f"File âm thanh tham chiếu không tồn tại: {reference_audio}")
      print(json.dumps({"type": "error",
                        "message": f"File âm thanh tham chiếu không tồn tại: {os.path.basename(reference_audio)}"}))
      return None

    # Tạo điều kiện tiềm ẩn
    try:
      log.info("Tạo embedding từ file tham chiếu")
      print(json.dumps(
          {"type": "info", "message": "Tạo embedding từ file tham chiếu..."}))

      gpt_cond_latent, speaker_embedding = model.get_conditioning_latents(
          audio_path=reference_audio,
          gpt_cond_len=model.config.gpt_cond_len,
          max_ref_length=model.config.max_ref_len,
          sound_norm_refs=model.config.sound_norm_refs
      )
    except Exception as e:
      log.error(f"Lỗi khi tạo embedding: {str(e)}")
      log.error(traceback.format_exc())
      print(json.dumps(
          {"type": "error", "message": f"Lỗi khi tạo embedding: {str(e)}"}))
      return None

    # Tổng hợp giọng nói sử dụng execute_with_best_device_and_setup
    try:
      log.info(f"Tổng hợp giọng nói cho văn bản: {text}")
      print(json.dumps({"type": "info",
                        "message": f"Tổng hợp giọng nói cho văn bản: {text}"}))

      # Tạo hàm inference để thực thi trên thiết bị tốt nhất
      def inference_func(device=None):
        return model.inference(
            text=text,
            language=language,
            gpt_cond_latent=gpt_cond_latent,
            speaker_embedding=speaker_embedding,
            temperature=model.config.temperature,
            length_penalty=model.config.length_penalty,
            repetition_penalty=model.config.repetition_penalty,
            top_k=model.config.top_k,
            top_p=model.config.top_p,
        )

      # Thực thi inference trên thiết bị tốt nhất
      out = execute_with_best_device_and_setup(inference_func)
    except Exception as e:
      log.error(f"Lỗi khi tổng hợp giọng nói: {str(e)}")
      log.error(traceback.format_exc())
      print(json.dumps({"type": "error",
                        "message": f"Lỗi khi tổng hợp giọng nói: {str(e)}"}))
      return None

    # Lưu âm thanh
    try:
      output_dir = os.path.dirname(config_path)
      output_file = os.path.join(output_dir, "test_output.wav")
      os.makedirs(output_dir, exist_ok=True)

      out["wav"] = torch.tensor(out["wav"]).unsqueeze(0)
      torchaudio.save(output_file, out["wav"], 24000)

      log.info(f"Đã lưu file âm thanh tại: {output_file}")
      print(json.dumps({"type": "success",
                        "message": f"Đã lưu file âm thanh tại: {output_file}"}))
    except Exception as e:
      log.error(f"Lỗi khi lưu file âm thanh: {str(e)}")
      log.error(traceback.format_exc())
      print(json.dumps(
          {"type": "error", "message": f"Lỗi khi lưu file âm thanh: {str(e)}"}))
      return None

    # Xóa cache GPU
    clear_gpu_cache()

    return output_file

  except Exception as e:
    log.error(f"Lỗi khi kiểm tra mô hình: {str(e)}")
    log.error(traceback.format_exc())
    print(json.dumps(
        {"type": "error", "message": f"Lỗi khi kiểm tra mô hình: {str(e)}"}))
    return None


def main():
  """Hàm chính xử lý tham số dòng lệnh và thực hiện fine-tune."""
  parser = argparse.ArgumentParser(description='Finetune XTTS model')
  parser.add_argument('--train_metadata', required=True,
                      help='Path to train metadata CSV file')
  parser.add_argument('--eval_metadata', required=True,
                      help='Path to evaluation metadata CSV file')
  parser.add_argument('--output', required=True,
                      help='Output folder for the finetuned model')
  parser.add_argument('--name', required=True,
                      help='Name for the finetuned voice')
  parser.add_argument('--language', default="en",
                      help='Language of the dataset (en, vi, etc.)')
  parser.add_argument('--epochs', type=int, default=10,
                      help='Number of training epochs')
  parser.add_argument('--batch_size', type=int, default=2,
                      help='Batch size for training')
  parser.add_argument('--test_text',
                      help='Text to test the model after training')
  parser.add_argument('--debug', action='store_true',
                      help='Enable debug mode with more verbose logging')
  parser.add_argument('--version', action='store_true',
                      help='Show version information and exit')

  args = parser.parse_args()

  # Hiển thị phiên bản và thoát nếu được yêu cầu
  if args.version:
    version_info = {
      "finetune_script": "1.0.0",
      "python": platform.python_version(),
      "torch": torch.__version__,
      "system": f"{platform.system()} {platform.release()}"
    }
    print(json.dumps({"type": "version", "data": version_info}))
    return 0

  # Sử dụng device_manager để tự động chọn thiết bị tốt nhất
  device_manager = get_device_manager()
  device = device_manager.get_current_device()

  # Kiểm tra môi trường
  try:
    check_memory()
  except Exception as e:
    log.warning(f"Không thể kiểm tra bộ nhớ: {str(e)}")

  # Hiển thị thông tin về thiết bị
  log.info(f"Sử dụng thiết bị: {device}")
  log.info(f"PyTorch version: {torch.__version__}")

  # Lấy thông tin chi tiết về thiết bị từ device_manager
  device_info = device_manager.get_device_info()
  log.info(f"Thông tin thiết bị: {device_info['name'].upper()}")

  try:
    # Chuẩn hóa đường dẫn
    train_metadata = os.path.abspath(args.train_metadata)
    eval_metadata = os.path.abspath(args.eval_metadata)
    output_folder = os.path.abspath(args.output)

    # Kiểm tra các file metadata
    if not os.path.exists(train_metadata) or not os.path.exists(eval_metadata):
      log.error(
          f"Không tìm thấy file metadata: {train_metadata} hoặc {eval_metadata}")
      print(json.dumps(
          {"type": "error", "message": "Không tìm thấy file metadata"}))
      return 1

    # Đọc metadata
    try:
      df_train = pd.read_csv(train_metadata, sep="|")
      df_eval = pd.read_csv(eval_metadata, sep="|")

      log.info(
          f"Đã đọc {len(df_train)} mẫu huấn luyện và {len(df_eval)} mẫu đánh giá")
      print(json.dumps({"type": "info",
                        "message": f"Đã đọc {len(df_train)} mẫu huấn luyện và {len(df_eval)} mẫu đánh giá"}))
    except Exception as e:
      log.error(f"Lỗi khi đọc file metadata: {str(e)}")
      print(json.dumps(
          {"type": "error", "message": f"Lỗi khi đọc file metadata: {str(e)}"}))
      return 1

    # Tạo thư mục output nếu chưa tồn tại
    os.makedirs(output_folder, exist_ok=True)

    # Tiến hành fine-tune
    config_path, vocab_file, checkpoint_path, speaker_wav = train_xtts_model(
        language=args.language,
        train_csv=train_metadata,
        eval_csv=eval_metadata,
        num_epochs=args.epochs,
        batch_size=args.batch_size,
        grad_acumm=DEFAULT_CONFIG["grad_acumm"],
        output_path=output_folder,
        max_audio_length=DEFAULT_CONFIG["max_audio_length"],
        voice_name=args.name
    )

    if checkpoint_path is None:
      error_msg = "Không thể huấn luyện mô hình. Dừng quá trình."
      log.error(error_msg)
      print(json.dumps({"type": "error", "message": error_msg}))
      return 1

    # Kiểm tra mô hình
    if args.test_text:
      output_wav = test_tts_model(
          checkpoint_path=checkpoint_path,
          config_path=config_path,
          vocab_path=vocab_file,
          text=args.test_text,
          language=args.language,
          reference_audio=speaker_wav
      )

    # Tìm tất cả các checkpoint
    checkpoints = []
    try:
      checkpoint_files = [f for f in os.listdir(output_folder) if
                          f.startswith("checkpoint_") and f.endswith(".pth")]
      if checkpoint_files:
        # Sắp xếp theo số bước
        checkpoint_files.sort(key=lambda x: int(x.split("_")[1].split(".")[0]))

        # Thêm thông tin về mỗi checkpoint
        for checkpoint_file in checkpoint_files:
          checkpoint_path = os.path.join(output_folder, checkpoint_file)
          epoch = int(checkpoint_file.split("_")[1].split(".")[0])

          # Tạo preview cho mỗi checkpoint
          preview_path = os.path.join(output_folder, f"preview_{epoch}.wav")
          test_tts_model(
              checkpoint_path=checkpoint_path,
              config_path=config_path,
              vocab_path=vocab_file,
              text=args.test_text or DEFAULT_CONFIG["test_text"][args.language],
              language=args.language,
              reference_audio=speaker_wav,
              output_path=preview_path
          )

          checkpoints.append({
            "epoch": epoch,
            "model_path": checkpoint_path,
            "config_path": config_path,
            "vocab_path": vocab_file,
            "preview_path": preview_path
          })
    except Exception as e:
      log.warning(f"Không thể tạo preview cho các checkpoint: {str(e)}")

    result = {
      "status": "success",
      "model_path": checkpoint_path,
      "config_path": config_path,
      "vocab_path": vocab_file,
      "voice_name": args.name,
      "checkpoints": checkpoints
    }
    log.data("Fine-tune hoàn thành thành công", result, type="complete")
    return 0
  except KeyboardInterrupt:
    log.warning("Quá trình bị ngắt bởi người dùng", {"signal": "SIGINT"})
    return 130  # Mã thoát cho SIGINT
  except Exception as e:
    error_msg = f"Lỗi không xử lý được: {str(e)}"
    log.error(error_msg, {
      "exception": str(e),
      "traceback": traceback.format_exc()
    })
    return 1


if __name__ == "__main__":
  sys.exit(main())
