#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Logger module for Gen Voice Python scripts.
Provides a unified logging interface that sends logs to Node.js.
"""

import json
import logging
import sys
import threading
import traceback
from typing import Any, Optional

# Đ<PERSON>nh nghĩa các level
LOG_LEVELS = {
  "debug": logging.DEBUG,
  "info": logging.INFO,
  "warning": logging.WARNING,
  "error": logging.ERROR
}

# Định nghĩa các channel
LOG_CHANNELS = [
  "system",
  "metadata",
  "finetune",
  "tts",
  "general"
]


class GenVoiceLogger:
  """
  Logger cho Gen Voice Python scripts.
  Gửi log về Node.js thông qua stdout/stderr với định dạng JSON.
  """

  def __init__(
      self,
      channel: str = "general",
      process_id: Optional[str] = None,
      min_level: str = "info"
  ):
    if channel not in LOG_CHANNELS:
      raise ValueError(
          f"Channel không hợp lệ: {channel}. Phải là một trong {LOG_CHANNELS}")

    if min_level not in LOG_LEVELS:
      raise ValueError(
          f"Level không hợp lệ: {min_level}. Phải là một trong {list(LOG_LEVELS.keys())}")

    self.channel = channel
    self.process_id = process_id
    self.min_level = LOG_LEVELS[min_level]
    self.lock = threading.Lock()

  def set_channel(self, channel: str) -> "GenVoiceLogger":
    if channel not in LOG_CHANNELS:
      raise ValueError(
          f"Channel không hợp lệ: {channel}. Phải là một trong {LOG_CHANNELS}")

    self.channel = channel
    return self

  def set_process_id(self, process_id: str) -> "GenVoiceLogger":
    self.process_id = process_id
    return self

  def debug(self, message: str, data: Any = None,
      channel: Optional[str] = None) -> "GenVoiceLogger":
    self._log("debug", message, data, channel)
    return self

  def info(self, message: str, data: Any = None,
      channel: Optional[str] = None) -> "GenVoiceLogger":
    self._log("info", message, data, channel)
    return self

  def data(self, message: str, data: Any = None, channel: Optional[str] = None,
      type: str = "progress") -> "GenVoiceLogger":
    self._log("info", message, data, channel, type)
    return self

  def warning(self, message: str, data: Any = None,
      channel: Optional[str] = None) -> "GenVoiceLogger":
    self._log("warning", message, data, channel)
    return self

  def error(self, message: str, data: Any = None,
      channel: Optional[str] = None) -> "GenVoiceLogger":
    self._log("error", message, data, channel)
    return self

  def _log(self, level: str, message: str, data: Any = None,
      channel: Optional[str] = None, type: str = None) -> None:
    if LOG_LEVELS[level] < self.min_level:
      return

    if channel is not None and channel not in LOG_CHANNELS:
      raise ValueError(
          f"Channel không hợp lệ: {channel}. Phải là một trong {LOG_CHANNELS}")

    log_entry = {
      "level": level,
      "channel": channel or self.channel,
      "message": message,
      "processId": self.process_id,
    }

    if data is not None:
      log_entry["data"] = data

    if type is not None:
      log_entry["type"] = type

    # Sử dụng lock để đảm bảo log không bị xáo trộn trong môi trường đa luồng
    with self.lock:
      # Sử dụng ensure_ascii=False để đảm bảo ký tự Unicode được giữ nguyên
      json_str = json.dumps(log_entry, ensure_ascii=False)
      if level == "error":
        print(f"{json_str}", file=sys.stderr)
        sys.stderr.flush()
      else:
        print(f"{json_str}", file=sys.stdout)
        sys.stdout.flush()


# Tạo instance mặc định của logger
x = GenVoiceLogger()


# Tạo handler cho thư viện logging của Python
class GenVoiceLogHandler(logging.Handler):
  """
  Handler cho thư viện logging của Python.
  Chuyển tiếp log từ logging module đến GenVoiceLogger.
  """

  def __init__(self, gv_logger: Optional[GenVoiceLogger] = None,
      channel: str = "general"):
    """
    Khởi tạo handler.

    Args:
        gv_logger: GenVoiceLogger instance
        channel: Kênh log mặc định
    """
    super().__init__()
    self.gv_logger = gv_logger or logger
    self.channel = channel

  def emit(self, record: logging.LogRecord) -> None:
    """
    Xử lý log record.

    Args:
        record: LogRecord từ logging module
    """
    # Chuyển đổi level
    if record.levelno >= logging.ERROR:
      level = "error"
    elif record.levelno >= logging.WARNING:
      level = "warning"
    elif record.levelno >= logging.INFO:
      level = "info"
    else:
      level = "debug"

    # Tạo message
    message = self.format(record)

    # Tạo data nếu có exception
    data = None
    if record.exc_info:
      data = {
        "exception": str(record.exc_info[1]),
        "traceback": "".join(traceback.format_exception(*record.exc_info))
      }

    # Ghi log
    self.gv_logger._log(level, message, data, self.channel)


def setup_logging(
    channel: str = "general",
    process_id: Optional[str] = None,
    min_level: str = "info",
    capture_standard_loggers: bool = True
) -> GenVoiceLogger:
  """
  Thiết lập logging cho module hiện tại.

  Args:
      channel: Kênh log mặc định
      process_id: ID của tiến trình
      min_level: Level tối thiểu để log
      capture_standard_loggers: Có capture root logger không

  Returns:
      GenVoiceLogger: Logger instance
  """
  # Tạo logger mới
  gv_logger = GenVoiceLogger(channel, process_id, min_level)

  # Capture root logger nếu cần
  if capture_standard_loggers:
    root_logger = logging.getLogger()

    # Xóa các handler hiện có
    for handler in root_logger.handlers[:]:
      root_logger.removeHandler(handler)

    # Thêm GenVoiceLogHandler
    handler = GenVoiceLogHandler(gv_logger, channel)
    handler.setFormatter(logging.Formatter('%(message)s'))
    root_logger.addHandler(handler)

    # Thiết lập level
    root_logger.setLevel(LOG_LEVELS[min_level])

  return gv_logger


# Hàm tiện ích để tạo logger cho một module
def get_logger(
    name: str,
    channel: str = "general",
    process_id: Optional[str] = None,
    min_level: str = "info"
) -> GenVoiceLogger:
  """
  Tạo logger cho một module.

  Args:
      name: Tên của module
      channel: Kênh log mặc định
      process_id: ID của tiến trình
      min_level: Level tối thiểu để log

  Returns:
      GenVoiceLogger: Logger instance
  """
  return GenVoiceLogger(channel, process_id, min_level)


# Tạo instance mặc định của logger
logger = get_logger("general")

# Export các hàm và biến cần thiết
__all__ = ["get_logger"]
