#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Device Manager - <PERSON><PERSON><PERSON><PERSON> lý thiết bị tính toán cho các tác vụ AI

Module này cung cấp các công cụ để:
1. <PERSON><PERSON><PERSON> hiện thiết bị tính toán tốt nhất (CUDA -> MPS -> CPU)
2. <PERSON>h<PERSON><PERSON> thi hàm trên thiết bị tốt nhất với cơ chế retry tự động
3. Cung cấp thông tin về thiết bị đang sử dụng
"""

import functools
import gc
import os
import platform
import shutil
import traceback
from typing import Callable, Any, Dict, List, Optional, Tuple
import psutil
import torch
from logger import get_logger

# Khởi tạo logger
logger = get_logger("device_manager", "system")

# Định nghĩa các loại thiết bị
DEVICE_CUDA = "cuda"
DEVICE_MPS = "mps"
DEVICE_CPU = "cpu"

# Thứ tự ưu tiên thiết bị (từ cao đến thấp)
DEVICE_PRIORITY = [DEVICE_CUDA, DEVICE_MPS, DEVICE_CPU]


class DeviceManager:
  """
  Quản lý thiết bị tính toán cho các tác vụ AI.

  Class này cung cấp các phương thức để phát hiện thiết bị tốt nhất
  và thực thi hàm trên thiết bị đó với cơ chế retry tự động.
  """

  def __init__(self, force_device: Optional[str] = None):
    """
    Khởi tạo DeviceManager.

    Args:
        force_device: Nếu được chỉ định, sẽ sử dụng thiết bị này thay vì tự động phát hiện
                      Giá trị hợp lệ: "cuda", "mps", "cpu"
    """
    self.available_devices = self._detect_available_devices()

    if force_device:
      if force_device not in DEVICE_PRIORITY:
        raise ValueError(
          f"Thiết bị không hợp lệ: {force_device}. Các thiết bị hợp lệ: {DEVICE_PRIORITY}")

      if force_device not in self.available_devices:
        logger.warning(
          f"Thiết bị {force_device} không khả dụng trên hệ thống này.")
        self.best_device = self._get_best_available_device()
      else:
        self.best_device = force_device
    else:
      self.best_device = self._get_best_available_device()

    logger.info(f"Thiết bị tốt nhất được phát hiện: {self.best_device.upper()}")
    logger.info(
      f"Các thiết bị khả dụng: {', '.join(dev.upper() for dev in self.available_devices)}")

    # Lưu trữ thông tin về thiết bị đang sử dụng
    self.current_device = self.best_device
    self.device_info = self._get_device_info(self.current_device)

  def _detect_available_devices(self) -> List[str]:
    """
    Phát hiện các thiết bị khả dụng trên hệ thống.

    Returns:
        List[str]: Danh sách các thiết bị khả dụng
    """
    available_devices = []

    # Kiểm tra CUDA
    try:
      if torch.cuda.is_available():
        available_devices.append(DEVICE_CUDA)
        logger.debug(f"CUDA khả dụng: {torch.cuda.get_device_name(0)}")
    except (ImportError, Exception) as e:
      logger.debug(f"Không thể kiểm tra CUDA: {str(e)}")

    # Kiểm tra MPS (Apple Silicon)
    try:
      if hasattr(torch, 'backends') and hasattr(torch.backends,
                                                'mps') and torch.backends.mps.is_available():
        available_devices.append(DEVICE_MPS)
        logger.debug("MPS khả dụng (Apple Silicon)")
    except (ImportError, Exception) as e:
      logger.debug(f"Không thể kiểm tra MPS: {str(e)}")

    # CPU luôn khả dụng
    available_devices.append(DEVICE_CPU)

    return available_devices

  def _get_best_available_device(self) -> str:
    """
    Lấy thiết bị tốt nhất từ các thiết bị khả dụng.

    Returns:
        str: Thiết bị tốt nhất
    """
    for device in DEVICE_PRIORITY:
      if device in self.available_devices:
        return device

    # Mặc định là CPU
    return DEVICE_CPU

  def _get_device_info(self, device: str) -> Dict[str, Any]:
    """
    Lấy thông tin chi tiết về thiết bị.

    Args:
        device: Tên thiết bị

    Returns:
        Dict[str, Any]: Thông tin về thiết bị
    """
    info = {
      "name": device,
      "type": device,
      "details": {}
    }

    try:
      if device == DEVICE_CUDA and torch.cuda.is_available():
        info["details"] = {
          "name": torch.cuda.get_device_name(0),
          "count": torch.cuda.device_count(),
          "memory_allocated": torch.cuda.memory_allocated(0),
          "memory_reserved": torch.cuda.memory_reserved(0),
          "max_memory_allocated": torch.cuda.max_memory_allocated(0),
          "max_memory_reserved": torch.cuda.max_memory_reserved(0),
        }

      elif device == DEVICE_MPS and hasattr(torch.backends,
                                            'mps') and torch.backends.mps.is_available():
        info["details"] = {
          "name": "Apple Silicon",
          "is_built": torch.backends.mps.is_built(),
        }

      elif device == DEVICE_CPU:
        info["details"] = {
          "name": platform.processor(),
          "cores": psutil.cpu_count(logical=False),
          "logical_cores": psutil.cpu_count(logical=True),
          "memory_total": psutil.virtual_memory().total,
          "memory_available": psutil.virtual_memory().available,
        }
    except ImportError:
      logger.warning(
        f"Không thể lấy thông tin chi tiết về thiết bị {device}. Thiếu thư viện cần thiết.")
    except Exception as e:
      logger.warning(
        f"Lỗi khi lấy thông tin chi tiết về thiết bị {device}: {str(e)}")

    return info

  def execute_with_device(self, func: Callable, *args, **kwargs) -> Any:
    """
    Thực thi hàm trên thiết bị tốt nhất với cơ chế retry tự động.

    Args:
        func: Hàm cần thực thi
        *args: Tham số vị trí cho hàm
        **kwargs: Tham số từ khóa cho hàm

    Returns:
        Any: Kết quả của hàm

    Raises:
        Exception: Nếu không thể thực thi hàm trên bất kỳ thiết bị nào
    """
    # Lấy danh sách thiết bị để thử, bắt đầu từ thiết bị tốt nhất
    devices_to_try = []
    for device in DEVICE_PRIORITY:
      if device in self.available_devices:
        devices_to_try.append(device)

    # Kiểm tra xem hàm có chấp nhận tham số device không
    import inspect
    func_signature = inspect.signature(func)
    accepts_device = 'device' in func_signature.parameters

    # Thêm device vào kwargs chỉ khi hàm chấp nhận tham số device
    if accepts_device and "device" not in kwargs:
      kwargs["device"] = self.best_device

    # Lưu lỗi cuối cùng để ném ra nếu tất cả các thiết bị đều thất bại
    last_error = None

    # Thử từng thiết bị theo thứ tự ưu tiên
    for device in devices_to_try:
      try:
        logger.info(f"Thử thực thi trên {device.upper()}")

        # Cập nhật thiết bị hiện tại
        self.current_device = device
        self.device_info = self._get_device_info(device)

        # Cập nhật device trong kwargs chỉ khi hàm chấp nhận tham số device
        if accepts_device:
          kwargs["device"] = device

        # Thực thi hàm
        result = func(*args, **kwargs)

        logger.info(f"Thực thi thành công trên {device.upper()}")
        return result

      except Exception as e:
        logger.warning(f"Lỗi khi thực thi trên {device.upper()}: {str(e)}")
        logger.debug(traceback.format_exc())
        last_error = e

    # Nếu tất cả các thiết bị đều thất bại
    error_msg = f"Không thể thực thi hàm trên bất kỳ thiết bị nào. Lỗi cuối cùng: {str(last_error)}"
    logger.error(error_msg)
    raise Exception(error_msg)

  def get_device_info(self) -> Dict[str, Any]:
    """
    Lấy thông tin về thiết bị đang sử dụng.

    Returns:
        Dict[str, Any]: Thông tin về thiết bị
    """
    return self.device_info

  def get_current_device(self) -> str:
    """
    Lấy tên thiết bị đang sử dụng.

    Returns:
        str: Tên thiết bị
    """
    return self.current_device

  def reset_to_best_device(self) -> None:
    """
    Đặt lại thiết bị hiện tại về thiết bị tốt nhất.
    """
    self.current_device = self.best_device
    self.device_info = self._get_device_info(self.best_device)
    logger.info(
      f"Đã đặt lại thiết bị về thiết bị tốt nhất: {self.best_device.upper()}")

  def check_memory(self) -> Dict[str, Any]:
    """
    Kiểm tra bộ nhớ hệ thống và GPU.

    Returns:
        Dict[str, Any]: Thông tin về bộ nhớ
    """
    try:
      import psutil
      memory_info = {}

      # Kiểm tra RAM
      system_memory = psutil.virtual_memory()
      memory_info["system_total_gb"] = system_memory.total / (1024 ** 3)
      memory_info["system_available_gb"] = system_memory.available / (1024 ** 3)
      memory_info["system_percent"] = system_memory.percent

      logger.info(
          f"RAM: {memory_info['system_available_gb']:.2f} GB / {memory_info['system_total_gb']:.2f} GB ({memory_info['system_percent']}% đã sử dụng)")

      # Kiểm tra VRAM nếu có CUDA
      try:
        if torch.cuda.is_available():
          for i in range(torch.cuda.device_count()):
            gpu_props = torch.cuda.get_device_properties(i)
            memory_info[f"gpu_{i}_name"] = gpu_props.name
            memory_info[f"gpu_{i}_total_gb"] = gpu_props.total_memory / (
                  1024 ** 3)
            memory_info[f"gpu_{i}_free_gb"] = torch.cuda.get_device_properties(
                i).total_memory / (1024 ** 3)  # Approximate

            logger.info(
                f"GPU {i} ({gpu_props.name}): {memory_info[f'gpu_{i}_total_gb']:.2f} GB")
      except ImportError:
        logger.debug("Không thể kiểm tra VRAM (torch không được cài đặt)")

      return memory_info
    except ImportError:
      logger.warning("Không thể kiểm tra bộ nhớ (psutil không được cài đặt)")
      return {"system_available_gb": 0, "system_total_gb": 0,
              "system_percent": 0}
    except Exception as e:
      logger.error(f"Lỗi khi kiểm tra bộ nhớ: {str(e)}")
      return {"system_available_gb": 0, "system_total_gb": 0,
              "system_percent": 0}

  def check_disk_space(self, path: str, required_gb: float = 10.0) -> bool:
    """
    Kiểm tra không gian ổ cứng còn trống.

    Args:
        path: Đường dẫn cần kiểm tra
        required_gb: Dung lượng tối thiểu cần thiết (GB)

    Returns:
        bool: True nếu đủ dung lượng, False nếu không đủ
    """
    try:
      if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)

      disk = shutil.disk_usage(path)
      free_gb = disk.free / (1024 ** 3)  # Chuyển byte sang GB

      logger.info(f"Dung lượng trống tại {path}: {free_gb:.2f} GB")

      if free_gb < required_gb:
        logger.warning(
            f"Không đủ dung lượng trống! Cần {required_gb} GB, còn trống {free_gb:.2f} GB")
        return False

      return True
    except Exception as e:
      logger.error(f"Lỗi khi kiểm tra dung lượng ổ cứng: {str(e)}")
      return True  # Trả về True để không chặn quá trình nếu không kiểm tra được


# Decorator để thực thi hàm trên thiết bị tốt nhất
def with_best_device(func: Callable) -> Callable:
  """
  Decorator để thực thi hàm trên thiết bị tốt nhất.

  Args:
      func: Hàm cần thực thi

  Returns:
      Callable: Hàm đã được wrap
  """

  @functools.wraps(func)
  def wrapper(*args, **kwargs):
    # Tạo DeviceManager nếu chưa có trong kwargs
    if "device_manager" not in kwargs:
      kwargs["device_manager"] = DeviceManager()

    # Thực thi hàm trên thiết bị tốt nhất
    return kwargs["device_manager"].execute_with_device(func, *args, **kwargs)

  return wrapper


# Singleton instance của DeviceManager
_device_manager_instance = None


def get_device_manager(force_device: Optional[str] = None) -> DeviceManager:
  """
  Lấy instance của DeviceManager (singleton pattern).

  Args:
      force_device: Nếu được chỉ định, sẽ sử dụng thiết bị này thay vì tự động phát hiện

  Returns:
      DeviceManager: Instance của DeviceManager
  """
  global _device_manager_instance

  if _device_manager_instance is None or force_device is not None:
    _device_manager_instance = DeviceManager(force_device)

  return _device_manager_instance


# Các hàm tiện ích ở cấp module

def execute_with_best_device(func: Callable, *args, **kwargs) -> Any:
  """
  Thực thi hàm trên thiết bị tốt nhất.

  Args:
      func: Hàm cần thực thi
      *args: Tham số vị trí cho hàm
      **kwargs: Tham số từ khóa cho hàm

  Returns:
      Any: Kết quả của hàm
  """
  device_manager = get_device_manager()

  # Không cần thiết lập môi trường vì đã được thiết lập trong file .env và main.py
  device = device_manager.get_current_device()

  return device_manager.execute_with_device(func, *args, **kwargs)


def check_memory() -> Dict[str, Any]:
  """
  Kiểm tra bộ nhớ hệ thống và GPU.

  Returns:
      Dict[str, Any]: Thông tin về bộ nhớ
  """
  device_manager = get_device_manager()
  return device_manager.check_memory()


def check_disk_space(path: str, required_gb: float = 10.0) -> bool:
  """
  Kiểm tra không gian ổ cứng còn trống.

  Args:
      path: Đường dẫn cần kiểm tra
      required_gb: Dung lượng tối thiểu cần thiết (GB)

  Returns:
      bool: True nếu đủ dung lượng, False nếu không đủ
  """
  device_manager = get_device_manager()
  return device_manager.check_disk_space(path, required_gb)


# Đã xóa hàm setup_device_environment vì các biến môi trường đã được thiết lập trong file .env và main.py


def clear_gpu_cache() -> None:
  """
  Xóa bộ nhớ cache GPU nếu có sẵn.

  Hàm này sẽ xóa cache CUDA nếu đang sử dụng GPU NVIDIA,
  hoặc thực hiện garbage collection nếu đang sử dụng MPS (Apple Silicon).
  """
  try:
    # Lấy thiết bị hiện tại từ device_manager
    device_manager = get_device_manager()
    device = device_manager.get_current_device()

    if device == "cuda":
      torch.cuda.empty_cache()
      logger.info("Đã xóa CUDA GPU cache")
    elif device == "mps":
      # MPS không có hàm clear cache rõ ràng như CUDA
      # Nhưng chúng ta có thể thử giải phóng bộ nhớ Python
      gc.collect()
      logger.info("Đã xóa bộ nhớ cho MPS (Apple Silicon)")

    # Thực hiện garbage collection bất kể thiết bị nào
    gc.collect()
  except Exception as e:
    logger.error(f"Lỗi khi xóa cache GPU: {str(e)}")


def move_model_to_device(model: Any, device: Optional[str] = None) -> Tuple[
  Any, str]:
  """
  Chuyển mô hình sang thiết bị phù hợp.

  Hàm này sẽ cố gắng chuyển mô hình sang thiết bị được chỉ định hoặc thiết bị tốt nhất.
  Nếu không thể chuyển, sẽ sử dụng CPU.

  Args:
      model: Mô hình cần chuyển
      device: Thiết bị đích, nếu None sẽ sử dụng thiết bị tốt nhất

  Returns:
      Tuple[Any, str]: (mô hình đã chuyển, thiết bị đang sử dụng)
  """
  try:
    # Không cần thiết lập môi trường vì đã được thiết lập trong file .env và main.py

    # Lấy thiết bị hiện tại nếu không được chỉ định
    if device is None:
      device_manager = get_device_manager()
      device = device_manager.get_current_device()

    # Chuyển mô hình sang thiết bị phù hợp
    if device == "cuda":
      # Sử dụng phương thức cuda() nếu có
      if hasattr(model, "cuda"):
        model.cuda()
        logger.info("Đã chuyển mô hình sang CUDA GPU")
      # Nếu không có phương thức cuda(), sử dụng to()
      elif hasattr(model, "to"):
        model.to(device)
        logger.info("Đã chuyển mô hình sang CUDA GPU bằng phương thức to()")
    elif device == "mps":
      # MPS có thể gặp vấn đề với một số mô hình
      try:
        if hasattr(model, "to"):
          model.to(device)
          logger.info("Đã chuyển mô hình sang MPS thành công")
        else:
          logger.warning(
            "Mô hình không có phương thức to(), sử dụng CPU thay thế")
          device = "cpu"
      except Exception as e:
        logger.warning(f"Không thể chuyển mô hình sang MPS: {str(e)}")
        logger.warning("Sử dụng CPU thay thế")
        device = "cpu"

    return model, device
  except Exception as e:
    logger.warning(f"Lỗi khi chuyển mô hình sang thiết bị: {str(e)}")
    logger.warning("Sử dụng CPU thay thế")
    return model, "cpu"


class ExecutableFunction:
  """
  Lớp bao bọc hàm để có thể truyền như một biến và thực thi sau.

  Sử dụng:
      func_obj = ExecutableFunction(my_func, arg1, arg2, kwarg1=value1)
      result = func_obj()  # Thực thi hàm với các tham số đã được truyền trước
  """

  def __init__(self, func: Callable, *args, **kwargs):
    """
    Khởi tạo với hàm và các tham số của nó.

    Args:
        func: Hàm cần thực thi
        *args: Tham số vị trí cho hàm
        **kwargs: Tham số từ khóa cho hàm
    """
    self.func = func
    self.args = args
    self.kwargs = kwargs
    self.__name__ = getattr(func, "__name__", "unknown_function")

  def __call__(self, *extra_args, **extra_kwargs):
    """
    Thực thi hàm với các tham số đã được truyền trước và các tham số bổ sung.

    Args:
        *extra_args: Tham số vị trí bổ sung
        **extra_kwargs: Tham số từ khóa bổ sung

    Returns:
        Any: Kết quả của hàm
    """
    # Kết hợp các tham số
    args = self.args + extra_args
    kwargs = {**self.kwargs, **extra_kwargs}

    # Thực thi hàm
    return self.func(*args, **kwargs)


def execute_with_best_device_and_setup(func: Callable, *args, **kwargs) -> Any:
  """
  Thực thi hàm trên thiết bị tốt nhất với các thiết lập môi trường tự động.

  Hàm này sẽ:
  1. Xóa cache GPU
  2. Thiết lập môi trường cho thiết bị
  3. Thực thi hàm trên thiết bị tốt nhất

  Args:
      func: Hàm cần thực thi
      *args: Tham số vị trí cho hàm
      **kwargs: Tham số từ khóa cho hàm

  Returns:
      Any: Kết quả của hàm
  """
  # Xóa cache GPU
  clear_gpu_cache()

  # Lấy device_manager
  device_manager = get_device_manager()
  device = device_manager.get_current_device()

  # Không cần thiết lập môi trường vì đã được thiết lập trong file .env và main.py

  logger.info(
    f"Sử dụng thiết bị: {device.upper()} cho việc thực thi {func.__name__}")

  # Thực thi hàm trên thiết bị tốt nhất
  return device_manager.execute_with_device(func, *args, **kwargs)


def create_executable_with_best_device(func: Callable, *args,
    **kwargs) -> ExecutableFunction:
  """
  Tạo một đối tượng hàm có thể thực thi trên thiết bị tốt nhất.

  Hàm này cho phép tạo một đối tượng hàm có thể truyền như một biến và thực thi sau.

  Args:
      func: Hàm cần thực thi
      *args: Tham số vị trí cho hàm
      **kwargs: Tham số từ khóa cho hàm

  Returns:
      ExecutableFunction: Đối tượng hàm có thể thực thi
  """
  return ExecutableFunction(execute_with_best_device, func, *args, **kwargs)


def create_executable_with_setup(func: Callable, *args,
    **kwargs) -> ExecutableFunction:
  """
  Tạo một đối tượng hàm có thể thực thi trên thiết bị tốt nhất với các thiết lập môi trường.

  Hàm này cho phép tạo một đối tượng hàm có thể truyền như một biến và thực thi sau.

  Args:
      func: Hàm cần thực thi
      *args: Tham số vị trí cho hàm
      **kwargs: Tham số từ khóa cho hàm

  Returns:
      ExecutableFunction: Đối tượng hàm có thể thực thi
  """
  return ExecutableFunction(execute_with_best_device_and_setup, func, *args,
                            **kwargs)


def execute_train_gpt(language: str, num_epochs: int, batch_size: int,
    grad_acumm: int,
    train_csv: str, eval_csv: str, output_path: str, max_audio_length: int):
  """
  Thực thi hàm train_gpt trên thiết bị tốt nhất với các thiết lập môi trường phù hợp.

  Args:
      language: Ngôn ngữ của dữ liệu
      num_epochs: Số epoch huấn luyện
      batch_size: Kích thước batch
      grad_acumm: Số bước tích lũy gradient
      train_csv: Đường dẫn đến file CSV chứa dữ liệu train
      eval_csv: Đường dẫn đến file CSV chứa dữ liệu đánh giá
      output_path: Đường dẫn đầu ra
      max_audio_length: Độ dài tối đa của âm thanh (mẫu)

  Returns:
      Tuple: Kết quả từ hàm train_gpt
  """
  # Import train_gpt
  try:
    from TTS.demos.xtts_ft_demo.utils.gpt_train import train_gpt
  except ImportError as e:
    logger.error(f"Không thể import module train_gpt: {str(e)}")
    raise ImportError(
      f"Không thể import module train_gpt. Hãy đảm bảo đã cài đặt TTS đúng cách.")

  # Thực thi train_gpt trên thiết bị tốt nhất với các thiết lập môi trường
  return execute_with_best_device_and_setup(
      train_gpt,
      language=language,
      num_epochs=num_epochs,
      batch_size=batch_size,  # batch_size sẽ được điều chỉnh trong finetune.py
      grad_acumm=grad_acumm,
      train_csv=train_csv,
      eval_csv=eval_csv,
      output_path=output_path,
      max_audio_length=max_audio_length
  )


# Ví dụ sử dụng
if __name__ == "__main__":
  # Ví dụ 1: Sử dụng DeviceManager trực tiếp
  def example_function(x, y, device=None):
    print(f"Thực thi trên thiết bị: {device}")
    return x + y


  try:
    # Tạo DeviceManager
    device_manager = DeviceManager()

    # Thực thi hàm
    result = device_manager.execute_with_device(example_function, 10, 20)
    print(f"Kết quả: {result}")

    # Lấy thông tin thiết bị
    device_info = device_manager.get_device_info()
    print(f"Thông tin thiết bị: {device_info}")
  except Exception as e:
    print(f"Lỗi: {str(e)}")


  # Ví dụ 2: Sử dụng decorator
  @with_best_device
  def decorated_function(x, y, device=None, device_manager=None):
    print(f"Thực thi trên thiết bị: {device}")
    return x * y


  try:
    result = decorated_function(10, 20)
    print(f"Kết quả: {result}")
  except Exception as e:
    print(f"Lỗi: {str(e)}")

  # Ví dụ 3: Sử dụng hàm tiện ích
  try:
    result = execute_with_best_device(example_function, 10, 30)
    print(f"Kết quả: {result}")
  except Exception as e:
    print(f"Lỗi: {str(e)}")
