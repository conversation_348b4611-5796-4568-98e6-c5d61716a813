declare module 'electron-store' {
  interface StoreOptions<T> {
    name?: string;
    defaults?: T;
    cwd?: string;
    fileExtension?: string;
    clearInvalidConfig?: boolean;
    accessPropertiesByDotNotation?: boolean;
    watch?: boolean;
    encryptionKey?: string | Buffer | TypedArray | DataView;
    schema?: any;
    migrations?: Record<string, (store: Store<T>) => void>;
    beforeEachMigration?: (store: Store<T>, context: any) => void;
    serialize?: (value: T) => string;
    deserialize?: (value: string) => T;
  }

  class Store<T extends Record<string, any> = Record<string, unknown>> {
    constructor(options?: StoreOptions<T>);
    static initRenderer(): void;
    get<U = T[keyof T]>(key: string, defaultValue?: any): U;
    set<U = T[keyof T]>(key: string, value: U): U;
    set(object: Partial<T>): void;
    has(key: string): boolean;
    reset(...keys: string[]): void;
    delete(key: string): void;
    clear(): void;
    onDidChange(key: string, callback: (newValue: any, oldValue: any) => void): () => void;
    onDidAnyChange(callback: (newValue: any, oldValue: any) => void): () => void;
    get size(): number;
    get store(): T;
    set store(value: T);
    get path(): string;
    openInEditor(): Promise<void>;
  }

  export default Store;
}
