<script setup lang="ts">
import {ref} from 'vue';
import LogViewer from '../components/LogViewer.vue';
import {logger} from '../utils/logger';

// State cho filter
const selectedChannel = ref<string>('');
const selectedLevel = ref<string>('');

// Hàm tạo log test
const createTestLog = () => {
  logger.debug('Đây là log debug test', {test: true}, 'system');
  logger.info('Đây là log info test', {test: true}, 'system');
  logger.warning('Đây là log warning test', {test: true}, 'system');
  logger.error('Đây là log error test', {test: true}, 'system');
};

// Hàm xóa toàn bộ log
const clearAllLogs = async () => {
  if (confirm('Bạn có chắc chắn muốn xóa toàn bộ log không?')) {
    try {
      await logger.clearLogs();
      alert('<PERSON><PERSON>t cả log đã được xóa thành công!');
    } catch (error) {
      console.error('Lỗi khi xóa logs:', error);
      alert('Có lỗi xảy ra khi xóa logs!');
    }
  }
};
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Logs</h1>
      <div class="flex space-x-2">
        <button
            @click="clearAllLogs"
            class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Xóa tất cả logs
        </button>
        <button
            @click="createTestLog"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Tạo log test
        </button>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div class="mb-4 flex flex-wrap gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Channel</label>
          <select
              v-model="selectedChannel"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option :value="null">Tất cả channels</option>
            <option value="system">System</option>
            <option value="metadata">Metadata</option>
            <option value="finetune">Finetune</option>
            <option value="tts">TTS</option>
            <option value="general">General</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Level</label>
          <select
              v-model="selectedLevel"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option :value="null">Tất cả levels</option>
            <option value="debug">Debug</option>
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
          </select>
        </div>
      </div>

      <LogViewer
          :channel="selectedChannel"
          :level="selectedLevel"
          title="System Logs"
          height="calc(100vh - 300px)"
          :showControls="false"
      />
    </div>
  </div>
</template>
