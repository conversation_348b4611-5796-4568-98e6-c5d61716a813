<script setup lang="ts">
import {onMounted, onUnmounted} from 'vue';
import {useAudioPlayer} from '../composables/useAudioPlayer';
import {useToast} from '../composables/useToast';
import TTSForm from '@renderer/components/tts/TTSForm.vue';
import TTSManager from '@renderer/components/tts/TTSManager.vue';


const {showToast} = useToast();
const {audioState, toggleAudio} = useAudioPlayer();

onMounted(() => {
  if (window.electronAPI) {
    // Lắng nghe sự kiện toast
    window.electronAPI.onToast(({type, message}) => {
      showToast(type, message);
    });
  }
});

onUnmounted(() => {
  // Dừng audio đang phát
  if (audioState.value.isPlaying) {
    toggleAudio('', () => {
    });
  }
});
</script>

<template>
  <div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 gap-8">
      <TTSForm/>
      <TTSManager/>
    </div>
  </div>
</template>
