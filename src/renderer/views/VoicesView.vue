<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import ManageVoices from '../components/voices/ManageVoices.vue';
import FinetuneVoice from '../components/voices/FinetuneVoice.vue';

const { t } = useI18n();

// State cho tab hiện tại
const activeTab = ref('manage'); // 'manage' hoặc 'finetune'
</script>

<template>
  <div>
    <div class="flex mb-6">
      <button
          @click="activeTab = 'manage'"
          class="px-4 py-2 rounded-l-md"
          :class="activeTab === 'manage' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'"
      >
        {{ t('voices.tabs.manage') }}
      </button>
      <button
          @click="activeTab = 'finetune'"
          class="px-4 py-2 rounded-r-md"
          :class="activeTab === 'finetune' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'"
      >
        {{ t('voices.tabs.finetune') }}
      </button>
    </div>

    <ManageVoices v-if="activeTab === 'manage'" />
    <FinetuneVoice v-if="activeTab === 'finetune'" />
  </div>
</template>
