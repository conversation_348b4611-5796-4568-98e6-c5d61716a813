<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useToast } from '@renderer/composables/useToast';

const { t } = useI18n();
const { showToast } = useToast();

// Hàm mở thư mục userData
const openUserDataFolder = async () => {
  try {
    await window.electronAPI.openUserDataFolder();
    showToast('success', t('settings.userDataFolder.openSuccess'));
  } catch (error) {
    console.error('Lỗi khi mở thư mục userData:', error);
    showToast('error', t('settings.userDataFolder.openError'));
  }
};
</script>

<template>
  <div>
    <h1 class="text-2xl font-bold mb-6 text-gray-800 dark:text-white">{{ t('settings.title') }}</h1>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <form>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

          <!-- UserData Folder -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('settings.userDataFolder.label') }}
            </label>
            <button
                type="button"
                @click="openUserDataFolder"
                class="w-full px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              {{ t('settings.userDataFolder.openButton') }}
            </button>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
              {{ t('settings.userDataFolder.description') }}
            </p>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>
