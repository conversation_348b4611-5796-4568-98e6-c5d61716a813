import {ref} from 'vue';
import type {TTSProcess} from '@types';
import {useToast} from './useToast';
import {useI18n} from 'vue-i18n';

export function useProcesses() {
  const { t } = useI18n();
  const { showToast } = useToast();

  const processes = ref<TTSProcess[]>([]);

  /**
   * Tạo filename duy nhất bằng cách thêm hậu tố số nếu cần
   * @returns Tên file duy nhất
   * @param ttsProcess
   */
  const generateUniqueFileName = async (ttsProcess: TTSProcess): Promise<string> => {
    const extension = '.wav';
    let fileName = ttsProcess.fileName;
    let counter = 1;

    // Kiểm tra file đầu tiên
    let fullPath = `${ttsProcess.outputPath}/${fileName}${extension}`;

    try {
      while (await isExistFile(fullPath)) {
        fileName = `${ttsProcess.fileName} (${counter})`;
        fullPath = `${ttsProcess.outputPath}/${fileName}${extension}`;
        counter++;

        if (counter > 10) {
          fileName = `${ttsProcess.fileName} (${ttsProcess.id.substring(0, 8)})`;
          break;
        }
      }
    } catch (error) {
      console.warn('Lỗi khi kiểm tra file tồn tại, sử dụng tên gốc:', error);
    }

    return fileName;
  };

  /**
   * Kiểm tra file có tồn tại không thông qua electronAPI
   * @param filePath Đường dẫn file
   * @returns Promise<boolean>
   */
  const isExistFile = async (filePath: string): Promise<boolean> => {
    try {
      if (!window.electronAPI?.isExistFile) {
        console.warn('electronAPI.isExistFile không khả dụng');
        return false;
      }
      return await window.electronAPI.isExistFile(filePath);
    } catch (error) {
      console.warn('Lỗi khi kiểm tra file tồn tại:', error);
      return false;
    }
  };

  // Load processes
  const loadProcesses = async () => {
    try {
      if (!window.electronAPI) {
        console.error('electronAPI không khả dụng');
        return;
      }
      const loadedProcesses = await window.electronAPI.getTTSProcesses();
      // console.log("loadedProcesses chunks: ", loadedProcesses.map(p => ({ id: p.id, chunksLength: p.chunks?.length || 0 })));
      if (loadedProcesses && Array.isArray(loadedProcesses)) {
        processes.value = loadedProcesses;
      } else {
        console.error('Invalid processes process:', loadedProcesses);
        showToast('error', t('home.process.errors.loadFailed'));
      }
    } catch (error) {
      console.error('Lỗi khi load danh sách processes:', error);
      showToast('error', t('home.process.errors.loadFailed'));
    }
  };

  const createProcess = async (process: TTSProcess) => {
    try {
      const uniqueFileName = await generateUniqueFileName(process);
      const newProcess = {...process, fileName: uniqueFileName};

      await window.electronAPI.createProcess(JSON.parse(JSON.stringify(newProcess)));

      window.electronAPI.textToSpeech({
        processId: newProcess.id,
        text: newProcess.text,
        voice: JSON.parse(JSON.stringify(newProcess.voice)),
        outputPath: newProcess.outputPath,
        fileName: newProcess.fileName,
        language: 'en',
        numThreads: newProcess.numThreads,
        numWorkers: newProcess.numWorkers
      });

    } catch (error) {
      console.error('Error creating or initiating process:', error);
      showToast('error', String(error));
    }
  };

  // Update process status
  const onUpdateProcess = async (process: TTSProcess) => {
    const processIndex = processes.value.findIndex((p) => p.id === process.id);
    
    // Tạo array mới thay vì mutate trực tiếp để tránh recursive updates
    const updatedProcesses = [...processes.value];
    
    if (processIndex !== -1) {
      updatedProcesses[processIndex] = {...process};
    } else {
      updatedProcesses.push(process);
    }
    
    processes.value = updatedProcesses;
  };

  // Cancel process
  const cancelProcess = async (processId: string) => {
    try {
      await window.electronAPI.cancelProcess(processId);
      showToast('success', t('home.process.actions.cancelSuccess'));
    } catch (error) {
      console.error('Error cancelling process:', error);
      showToast('error', String(error));
      throw error;
    }
  };

  // Delete process
  const deleteProcess = async (processId: string) => {
    try {
      await window.electronAPI.removeProcess(processId);
      processes.value = processes.value.filter(p => p.id !== processId);
      showToast('success', t('home.process.actions.deleteSuccess'));
    } catch (error) {
      console.error('Error deleting process:', error);
      showToast('error', String(error));
      throw error;
    }
  };

  // Cleanup processes - sử dụng API hàng loạt mới
  const cleanupProcesses = async () => {
    try {
      // Gọi API cleanup hàng loạt, trả về danh sách process còn lại
      processes.value = await window.electronAPI.cleanupCompletedProcesses();

      showToast('success', t('home.process.actions.cleanupSuccess'));
    } catch (error) {
      console.error('Error cleaning up processes:', error);
      showToast('error', t('home.process.actions.cleanupError'));
      throw error;
    }
  };

  const reorderProcesses = async (newOrder: string[]) => {
    try {
      // Update local state first
      const reorderedProcesses = newOrder.map(id => processes.value.find(p => p.id === id)).filter(Boolean) as TTSProcess[];
      processes.value = reorderedProcesses;

      // Then update backend
      await window.electronAPI.reorderProcesses(newOrder);
    } catch (error) {
      // If backend update fails, reload processes to ensure consistency
      console.error('Error reordering processes:', error);
      await loadProcesses();
      throw error;
    }
  };

  return {
    processes,
    loadProcesses,
    createProcess,
    onUpdateProcess,
    cancelProcess,
    deleteProcess,
    cleanupProcesses,
    reorderProcesses,
  };
}