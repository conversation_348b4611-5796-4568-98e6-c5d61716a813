import { ref } from 'vue';

const type = ref<'success' | 'error' | 'info' | 'warning' | ''>('');
const message = ref('');
const visible = ref(false);
let timeoutId: ReturnType<typeof setTimeout> | null = null;

function showToast(newType: 'success' | 'error' | 'info' | 'warning', newMessage: string) {
  if (visible.value && type.value === newType && message.value === newMessage) return;
  type.value = newType;
  message.value = newMessage;
  visible.value = true;
  if (timeoutId) clearTimeout(timeoutId);
  timeoutId = setTimeout(() => {
    visible.value = false;
    setTimeout(() => {
      type.value = '';
      message.value = '';
    }, 300);
  }, newType === 'error' ? 5000 : 3000);
}

function clearToast() {
  visible.value = false;
  if (timeoutId) clearTimeout(timeoutId);
  setTimeout(() => {
    type.value = '';
    message.value = '';
  }, 300);
}

export function useToast() {
  return { type, message, visible, showToast, clearToast };
} 