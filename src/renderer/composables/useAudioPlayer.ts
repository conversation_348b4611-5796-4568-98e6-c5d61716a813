import { ref } from 'vue';

interface AudioState {
  isPlaying: boolean;
  currentPath: string | null;
  onEnded?: () => void;
}

export function useAudioPlayer() {
  const currentAudio = ref<HTMLAudioElement | null>(null);
  const audioState = ref<AudioState>({
    isPlaying: false,
    currentPath: null
  });

  const getAudioUrl = (audioPath: string) => {
    // Luôn sử dụng local:// cho tất cả file local
    return `local://${audioPath}`;
  };

  const playAudio = async (audioPath: string, onEnded?: () => void) => {
    try {
      // Dừng audio đang phát (nếu có)
      if (currentAudio.value) {
        currentAudio.value.pause();
        currentAudio.value = null;
        audioState.value = {
          isPlaying: false,
          currentPath: null
        };
      }

      // Tạo audio mới với URL đã được xử lý
      const audio = new Audio(getAudioUrl(audioPath));
      
      audio.onerror = (error) => {
        console.error('Lỗi khi phát audio:', error);
        audioState.value = {
          isPlaying: false,
          currentPath: null
        };
        currentAudio.value = null;
        throw new Error('Không thể phát file âm thanh');
      };

      audio.onended = () => {
        audioState.value = {
          isPlaying: false,
          currentPath: null
        };
        currentAudio.value = null;
        if (onEnded) onEnded();
      };

      // Phát audio
      await audio.play();
      currentAudio.value = audio;
      audioState.value = {
        isPlaying: true,
        currentPath: audioPath,
        onEnded
      };
    } catch (error) {
      console.error('Lỗi khi phát audio:', error);
      audioState.value = {
        isPlaying: false,
        currentPath: null
      };
      currentAudio.value = null;
      throw error;
    }
  };

  const stopAudio = () => {
    if (currentAudio.value) {
      currentAudio.value.pause();
      currentAudio.value = null;
      audioState.value = {
        isPlaying: false,
        currentPath: null
      };
    }
  };

  const toggleAudio = async (audioPath: string, onEnded?: () => void) => {
    if (audioState.value.isPlaying && audioState.value.currentPath === audioPath) {
      stopAudio();
    } else {
      await playAudio(audioPath, onEnded);
    }
  };

  return {
    currentAudio,
    audioState,
    playAudio,
    stopAudio,
    toggleAudio
  };
} 