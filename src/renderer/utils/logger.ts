/**
 * Logger Client cho renderer process
 */

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu
type LogLevel = 'debug' | 'info' | 'warning' | 'error';
type LogChannel = 'system' | 'metadata' | 'finetune' | 'tts' | 'general';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  channel: LogChannel;
  message: string;
  data?: any;
  source: 'node' | 'python' | 'renderer';
  processId?: string;
}

// Kiểm tra xem logger API có tồn tại không
const hasLoggerAPI = typeof window !== 'undefined' && 'logger' in window;

/**
 * Logger Client cho renderer process
 */
class LoggerClient {
  private defaultChannel: LogChannel;
  private processId?: string;

  constructor(defaultChannel: LogChannel = 'general', processId?: string) {
    this.defaultChannel = defaultChannel;
    this.processId = processId;
  }

  /**
   * Ghi log debug
   */
  debug(message: string, data?: any, channel?: LogChannel) {
    this.log('debug', message, data, channel);
    return this;
  }

  /**
   * Ghi log info
   */
  info(message: string, data?: any, channel?: LogChannel) {
    this.log('info', message, data, channel);
    return this;
  }

  /**
   * Ghi log warning
   */
  warning(message: string, data?: any, channel?: LogChannel) {
    this.log('warning', message, data, channel);
    return this;
  }

  /**
   * Ghi log error
   */
  error(message: string, data?: any, channel?: LogChannel) {
    this.log('error', message, data, channel);
    return this;
  }

  /**
   * Ghi log với level tùy chọn
   */
  log(level: LogLevel, message: string, data?: any, channel?: LogChannel) {
    const actualChannel = channel || this.defaultChannel;

    // Ghi log vào console trước
    this.logToConsole(level, message, data, actualChannel);

    // Gửi log đến main process nếu có logger API
    if (hasLoggerAPI) {
      try {
        (window as any).logger[level](message, data, actualChannel);
      } catch (error) {
        console.error('Lỗi khi gửi log đến main process:', error);
      }
    }

    return this;
  }

  /**
   * Lấy tất cả logs
   */
  async getLogs(filter?: {
    level?: LogLevel;
    channel?: LogChannel;
    source?: 'node' | 'python' | 'renderer';
    processId?: string;
    startTime?: string;
    endTime?: string;
  }): Promise<LogEntry[]> {
    if (hasLoggerAPI) {
      try {
        return await (window as any).logger.getLogs(filter);
      } catch (error) {
        console.error('Lỗi khi lấy logs từ main process:', error);
        return [];
      }
    }
    return [];
  }

  /**
   * Xóa tất cả logs
   */
  async clearLogs(): Promise<boolean> {
    if (hasLoggerAPI) {
      try {
        return await (window as any).logger.clearLogs();
      } catch (error) {
        console.error('Lỗi khi xóa logs từ main process:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Đăng ký lắng nghe log mới
   */
  onNewLog(callback: (log: LogEntry) => void): () => void {
    if (hasLoggerAPI) {
      try {
        return (window as any).logger.onNewLog(callback);
      } catch (error) {
        console.error('Lỗi khi đăng ký lắng nghe log mới:', error);
      }
    }
    return () => {};
  }

  /**
   * Tạo một logger mới với channel khác
   */
  channel(channel: LogChannel) {
    return new LoggerClient(channel, this.processId);
  }

  /**
   * Ghi log vào console
   */
  private logToConsole(level: LogLevel, message: string, data?: any, channel?: LogChannel) {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${channel}]`;
    
    switch (level) {
      case 'debug':
        console.debug(`${prefix} ${message}`, data || '');
        break;
      case 'info':
        console.info(`${prefix} ${message}`, data || '');
        break;
      case 'warning':
        console.warn(`${prefix} ${message}`, data || '');
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data || '');
        break;
    }
  }
}

// Tạo instance mặc định của LoggerClient
export const logger = new LoggerClient('general');
