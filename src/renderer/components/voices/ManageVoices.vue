<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Voice } from '@types';
import { useI18n } from 'vue-i18n';
import { useToast } from '../../composables/useToast';
import { useAudioPlayer } from '../../composables/useAudioPlayer';
import UpdateVoice from './UpdateVoice.vue';

const { t } = useI18n();
const { showToast } = useToast();
const { audioState, toggleAudio } = useAudioPlayer();

// State cho danh sách voices
const voices = ref<Voice[]>([]);
// State cho trạng thái loading của từng voice
const exportingVoices = ref<Set<string>>(new Set());
const isImporting = ref(false);
const showImportDialog = ref(false);
const importVoiceName = ref('');
// State cho dialog cập nhật voice
const showUpdateDialog = ref(false);
const selectedVoice = ref<Voice | null>(null);

// <PERSON><PERSON> danh sách voices khi component được mount
onMounted(async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }
    const result = await window.electronAPI.getVoices();
    if (result) {
      voices.value = result;
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách voices:', error);
  }
});

// Hàm export voice
const exportVoice = async (voice: Voice) => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    // Kiểm tra voice có đủ thông tin cần thiết không
    if (!voice.configPath || !voice.vocabPath) {
      showToast('error', t('voices.manage.export.error.missingFiles'));
      return;
    }

    // Đánh dấu voice đang được export
    exportingVoices.value.add(voice.id);

    // Chọn thư mục đích
    const outputDir = await window.electronAPI.selectFolder();
    if (!outputDir) {
      exportingVoices.value.delete(voice.id);
      return;
    }

    // Gọi API export voice
    const result = await window.electronAPI.exportVoice(voice.id, outputDir);
    
    if (result) {
      showToast('success', t('voices.manage.export.success'));
    } else {
      showToast('error', t('voices.manage.export.error.failed'));
    }
  } catch (error) {
    console.error('Lỗi khi export voice:', error);
    showToast('error', t('voices.manage.export.error.failed'));
  } finally {
    // Xóa trạng thái loading
    exportingVoices.value.delete(voice.id);
  }
};

// Hàm import voice
const importVoice = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    isImporting.value = true;

    // Chọn thư mục import
    const importDir = await window.electronAPI.selectFolder();
    if (!importDir) {
      isImporting.value = false;
      return;
    }

    // Gọi API import voice với tham số được truyền dưới dạng object
    const result = await window.electronAPI.importVoice({
      importDir,
      voiceName: importVoiceName.value
    });
    
    if (result) {
      // Cập nhật lại danh sách voices
      const updatedVoices = await window.electronAPI.getVoices();
      if (updatedVoices) {
        voices.value = updatedVoices;
      }
      showToast('success', t('voices.manage.import.success'));
      showImportDialog.value = false;
      importVoiceName.value = '';
    } else {
      showToast('error', t('voices.manage.import.error.failed'));
    }
  } catch (error) {
    console.error('Lỗi khi import voice:', error);
    showToast('error', t('voices.manage.import.error.failed'));
  } finally {
    isImporting.value = false;
  }
};

// Hàm xử lý cập nhật voice
const handleUpdateVoice = (updatedVoice: Voice) => {
  const index = voices.value.findIndex(v => v.id === updatedVoice.id);
  if (index !== -1) {
    voices.value[index] = updatedVoice;
  }
};

// Hàm xử lý xóa voice
const handleVoiceDelete = (voiceId: string) => {
  voices.value = voices.value.filter(voice => voice.id !== voiceId);
};

// Hàm mở dialog cập nhật voice
const openUpdateDialog = (voice: Voice) => {
  selectedVoice.value = voice;
  showUpdateDialog.value = true;
};

// Hàm xử lý phát/dừng audio
const handlePlayAudio = async (voice: Voice) => {
  try {
    if (!voice.previewPath) {
      showToast('error', t('voices.manage.actions.noPreview'));
      return;
    }

    await toggleAudio(voice.previewPath);
  } catch (error) {
    console.error('Lỗi khi phát audio:', error);
    showToast('error', t('voices.manage.actions.playError'));
  }
};
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold text-gray-800 dark:text-white">{{ t('voices.title') }}</h2>
      <button 
        @click="showImportDialog = true"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        {{ t('voices.manage.actions.import') }}
      </button>
    </div>

    <!-- Dialog import voice -->
    <div v-if="showImportDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96">
        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">
          {{ t('voices.manage.import.title') }}
        </h3>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('voices.manage.import.nameLabel') }}
          </label>
          <input 
            v-model="importVoiceName"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            :placeholder="t('voices.manage.import.namePlaceholder')"
          />
        </div>
        <div class="flex justify-end space-x-2">
          <button 
            @click="showImportDialog = false"
            class="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
          >
            {{ t('common.cancel') }}
          </button>
          <button 
            @click="importVoice"
            :disabled="isImporting"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isImporting" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ t('common.importing') }}
            </span>
            <span v-else>{{ t('common.import') }}</span>
          </button>
        </div>
      </div>
    </div>

    <div v-if="voices.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
      {{ t('voices.manage.empty') }}
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div v-for="voice in voices" :key="voice.id" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex flex-col h-full">
          <div class="flex justify-between items-start">
            <div>
              <h3 class="font-medium text-gray-800 dark:text-white">{{ voice.name }}</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ voice.description || 'Không có mô tả' }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {{ new Date(voice.createdAt).toLocaleDateString() }}
              </p>
            </div>
            <div class="flex space-x-2">
              <button 
                @click="handlePlayAudio(voice)" 
                class="text-blue-500 hover:text-blue-600"
              >
                <span class="sr-only">{{ t('voices.manage.actions.play') }}</span>
                <svg v-if="audioState.isPlaying && audioState.currentPath === voice.previewPath" 
                     xmlns="http://www.w3.org/2000/svg" 
                     class="h-5 w-5" 
                     viewBox="0 0 20 20" 
                     fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                <svg v-else 
                     xmlns="http://www.w3.org/2000/svg" 
                     class="h-5 w-5" 
                     viewBox="0 0 20 20" 
                     fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                </svg>
              </button>
              <button @click="openUpdateDialog(voice)" class="text-yellow-500 hover:text-yellow-600">
                <span class="sr-only">{{ t('voices.manage.actions.edit') }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
              </button>
            </div>
          </div>
          
          <!-- Nút export ở góc phải dưới -->
          <div class="mt-auto pt-4 flex justify-end">
            <button v-if="voice.configPath && voice.vocabPath" 
                    @click="exportVoice(voice)"
                    :disabled="exportingVoices.has(voice.id)"
                    class="text-green-500 hover:text-green-600 disabled:opacity-50 disabled:cursor-not-allowed">
              <span class="sr-only">{{ t('voices.manage.actions.export') }}</span>
              <!-- Icon loading -->
              <svg v-if="exportingVoices.has(voice.id)" 
                   class="animate-spin h-5 w-5" 
                   xmlns="http://www.w3.org/2000/svg" 
                   fill="none" 
                   viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <!-- Icon export -->
              <svg v-else 
                   xmlns="http://www.w3.org/2000/svg" 
                   class="h-5 w-5" 
                   viewBox="0 0 20 20" 
                   fill="currentColor">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Dialog cập nhật voice -->
    <UpdateVoice
      v-if="selectedVoice"
      v-model:show="showUpdateDialog"
      :voice="selectedVoice"
      @update="handleUpdateVoice"
      @delete="handleVoiceDelete"
    />
  </div>
</template> 