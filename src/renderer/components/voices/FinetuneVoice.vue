<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import type { MetadataItem, MetadataStats, Voice } from '@types';
import { useI18n } from 'vue-i18n';
import { v4 as uuidv4 } from 'uuid';

const { t } = useI18n();

// State cho danh sách voices
const voices = ref<Voice[]>([]);

// State cho phần finetune
const formData = ref({
  inputFolder: '',
  voiceName: '',
  language: 'en'
});

// State cho tiến trình finetune
const finetuneStatus = ref<'idle' | 'processing' | 'completed' | 'failed'>('idle');
const finetuneProgress = ref(0);
const finetuneLogs = ref<string[]>([]);
const currentProcessId = ref<string | null>(null);
const logContainer = ref<HTMLElement | null>(null);

// State cho modal xác nhận hủy huấn luyện
const showCancelConfirmation = ref(false);

// State cho xử lý metadata
const metadataProcessing = ref(false);
const metadataProcessed = ref(false);
const metadataLogs = ref<string[]>([]);
const metadataItems = ref<MetadataItem[]>([]);
const metadataStats = ref<MetadataStats>({
  totalFiles: 0,
  totalDuration: 0,
  trainCount: 0,
  evalCount: 0
});
const metadataProcessId = ref<string | null>(null);
const trainMetadataPath = ref<string | null>(null);
const evalMetadataPath = ref<string | null>(null);
const metadataLogContainer = ref<HTMLElement | null>(null);

// Hàm chọn folder
const selectFolder = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    const folderPath = await window.electronAPI.selectFolder();
    if (folderPath) {
      formData.value.inputFolder = folderPath;
      // Reset trạng thái metadata
      metadataProcessed.value = false;
    }
  } catch (error) {
    console.error('Lỗi khi chọn folder:', error);
  }
};

// Hàm bắt đầu finetune
const startFineTune = async (e: Event) => {
  e.preventDefault();

  if (!formData.value?.inputFolder) {
    alert('Vui lòng chọn thư mục chứa file audio');
    return;
  }

  if (!formData.value?.voiceName) {
    alert('Vui lòng nhập tên giọng nói');
    return;
  }

  if (!metadataProcessed.value) {
    alert('Vui lòng xử lý tài nguyên huấn luyện trước');
    return;
  }

  if (!trainMetadataPath.value || !evalMetadataPath.value) {
    alert('Không tìm thấy file metadata. Vui lòng xử lý tài nguyên huấn luyện lại.');
    return;
  }

  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    // Reset state
    finetuneStatus.value = 'processing';
    finetuneProgress.value = 0;
    finetuneLogs.value = [];

    // Bắt đầu quá trình finetune
    const result = await window.electronAPI.startFinetune({
      voiceName: formData.value.voiceName,
      trainMetadataPath: trainMetadataPath.value ?? undefined,
      evalMetadataPath: evalMetadataPath.value ?? undefined,
      epochs: 30, // Có thể thêm UI để người dùng chọn số epoch
      testText: `Xin chào, đây là giọng nói ${formData.value.voiceName} sau khi được fine-tune.`
    });

    if ('processId' in result) {
      currentProcessId.value = result.processId;
      finetuneLogs.value.push(`Bắt đầu quá trình finetune voice: ${formData.value?.voiceName}`);
    } else if ('error' in result) {
      finetuneStatus.value = 'failed';
      finetuneLogs.value.push(`Lỗi: ${result.error}`);
    }
  } catch (error: unknown) {
    console.error('Lỗi khi bắt đầu finetune:', error);
    finetuneStatus.value = 'failed';

    // Xử lý lỗi với kiểm tra kiểu dữ liệu
    if (error instanceof Error) {
      finetuneLogs.value.push(`Lỗi: ${error.message}`);
    } else if (typeof error === 'string') {
      finetuneLogs.value.push(`Lỗi: ${error}`);
    } else {
      finetuneLogs.value.push('Lỗi: Không xác định');
    }
  }
};

// Hàm xử lý metadata
const processMetadata = async () => {
  try {
    if (!formData.value?.inputFolder) {
      alert('Vui lòng chọn thư mục chứa file audio');
      return;
    }

    if (!formData.value?.voiceName) {
      alert('Vui lòng nhập tên giọng nói');
      return;
    }

    // Kiểm tra electronAPI có khả dụng không
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      alert('Không thể kết nối với hệ thống. Vui lòng khởi động lại ứng dụng.');
      return;
    }

    // Đảm bảo các giá trị cần thiết đều tồn tại
    const inputFolder = formData.value?.inputFolder;
    const voiceName = formData.value?.voiceName;
    const language = formData.value?.language || 'en';

    if (!inputFolder || !voiceName) {
      console.error('Thiếu thông tin đầu vào');
      return;
    }

    // Bắt đầu xử lý metadata
    metadataProcessing.value = true;
    metadataLogs.value = [];
    metadataItems.value = [];

    // Hiển thị loading table ngay lập tức
    metadataItems.value = [{fileName: 'Loading...', text: 'Loading...', duration: 0}];

    // Tạo processId trước khi gọi API
    metadataProcessId.value = crypto.randomUUID();

    // Đăng ký event listener trước khi gọi API
    unsubscribeMetadataProgress = window.electronAPI.onMetadataProgress((data) => {
      // Cập nhật tiến trình xử lý metadata
      metadataLogs.value.push(`Đang xử lý: ${data.fileName} (${data.progress}%)`);
      metadataLogs.value.push(`Thời lượng: ${data.duration.toFixed(2)}s, Số đoạn: ${data.segments}`);
      metadataLogs.value.push(`Đã xử lý: ${data.totalFiles}/${data.totalFiles} file, ${data.totalSegments} đoạn`);
      metadataLogs.value.push(`Tổng thời lượng: ${data.totalDuration.toFixed(2)}s`);

      // Cập nhật danh sách metadata
      if (metadataItems.value.length === 1 && metadataItems.value[0].fileName === 'Loading...') {
        metadataItems.value = [];
      }

      // Thêm file mới vào danh sách
      metadataItems.value.push({
        fileName: data.fileName,
        text: data.text,
        duration: data.duration,
        segments: data.segments
      });
    });

    unsubscribeMetadataComplete = window.electronAPI.onMetadataComplete((data) => {
      // Hoàn thành xử lý metadata
      metadataProcessing.value = false;
      metadataProcessed.value = true;

      // Cập nhật đường dẫn metadata
      trainMetadataPath.value = data.trainPath;
      evalMetadataPath.value = data.evalPath;

      // Cập nhật thống kê
      metadataStats.value = {
        totalFiles: data.totalFiles,
        totalDuration: Math.round(data.totalDuration * 10) / 10,
        trainCount: data.trainCount,
        evalCount: data.evalCount
      };

      // Thêm log hoàn thành
      metadataLogs.value.push('---');
      metadataLogs.value.push(`Đã hoàn thành xử lý metadata!`);
      metadataLogs.value.push(`Tổng số file: ${metadataStats.value.totalFiles}`);
      metadataLogs.value.push(`Tổng thời lượng: ${metadataStats.value.totalDuration}s`);
      metadataLogs.value.push(`Số file huấn luyện: ${metadataStats.value.trainCount}`);
      metadataLogs.value.push(`Số file đánh giá: ${metadataStats.value.evalCount}`);
      metadataLogs.value.push(`Đường dẫn metadata train: ${trainMetadataPath.value}`);
      metadataLogs.value.push(`Đường dẫn metadata eval: ${evalMetadataPath.value}`);

      // Hủy đăng ký sự kiện
      if (unsubscribeMetadataProgress) {
        unsubscribeMetadataProgress();
        unsubscribeMetadataProgress = null;
      }
      if (unsubscribeMetadataComplete) {
        unsubscribeMetadataComplete();
        unsubscribeMetadataComplete = null;
      }
    });

    // Thêm log bắt đầu
    console.log('Bắt đầu xử lý metadata:', {inputFolder, voiceName, language});
    metadataLogs.value.push(`Bắt đầu xử lý metadata cho giọng nói: ${voiceName}`);
    metadataLogs.value.push(`Thư mục đầu vào: ${inputFolder}`);
    metadataLogs.value.push(`Ngôn ngữ: ${language}`);
    metadataLogs.value.push('---');

    // Gọi API sau khi đã đăng ký listener
    const result = await window.electronAPI.processMetadata({
      processId: metadataProcessId.value,
      inputFolder,
      voiceName,
      language
    });

    // Xử lý kết quả
    if (!result) {
      metadataProcessing.value = false;
      metadataLogs.value.push('Lỗi: Không nhận được kết quả từ server');
      return;
    }

    if ('error' in result) {
      // Xử lý lỗi
      metadataProcessing.value = false;
      metadataLogs.value.push(`Lỗi: ${result.error}`);
      return;
    }

  } catch (error: unknown) {
    console.error('Lỗi khi xử lý metadata:', error);
    metadataProcessing.value = false;

    // Xử lý lỗi với kiểm tra kiểu dữ liệu
    if (error instanceof Error) {
      metadataLogs.value.push(`Lỗi: ${error.message}`);
    } else if (typeof error === 'string') {
      metadataLogs.value.push(`Lỗi: ${error}`);
    } else {
      metadataLogs.value.push('Lỗi: Không xác định');
    }
  }
};

// Hàm hiển thị modal xác nhận hủy huấn luyện
const showCancelConfirmationModal = () => {
  showCancelConfirmation.value = true;
};

// Hàm đóng modal xác nhận
const closeCancelConfirmation = () => {
  showCancelConfirmation.value = false;
};

// Hàm hủy finetune sau khi xác nhận
const confirmCancelFineTune = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    if (currentProcessId.value) {
      const result = await window.electronAPI.cancelFinetune(currentProcessId.value);
      if (result) {
        finetuneStatus.value = 'failed';
        finetuneLogs.value.push('Đã hủy quá trình finetune');
      }
    }
    // Đóng modal xác nhận
    showCancelConfirmation.value = false;
  } catch (error) {
    console.error('Lỗi khi hủy finetune:', error);
    showCancelConfirmation.value = false;
  }
};

// Hàm hủy xử lý metadata
const cancelMetadataProcessing = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    if (metadataProcessId.value) {
      const result = await window.electronAPI.cancelFinetune(metadataProcessId.value);
      if (result) {
        metadataProcessing.value = false;
        metadataLogs.value.push('Đã hủy quá trình xử lý metadata');
      }
    }
  } catch (error: unknown) {
    console.error('Lỗi khi hủy xử lý metadata:', error);
    if (error instanceof Error) {
      metadataLogs.value.push(`Lỗi: ${error.message}`);
    } else {
      metadataLogs.value.push('Lỗi không xác định khi hủy xử lý metadata');
    }
  }
};

// Biến lưu trữ các hàm hủy đăng ký sự kiện
let unsubscribeProgress: (() => void) | null = null;
let unsubscribeLog: (() => void) | null = null;
let unsubscribeComplete: (() => void) | null = null;
let unsubscribeError: (() => void) | null = null;
let unsubscribeMetadataProgress: (() => void) | null = null;
let unsubscribeMetadataComplete: (() => void) | null = null;

// Theo dõi thay đổi của finetuneLogs để cuộn lên đầu khi có log mới
watch(finetuneLogs, () => {
  // Đảm bảo logContainer đã được render
  if (logContainer.value) {
    // Cuộn lên đầu (vì chúng ta đã đảo ngược thứ tự hiển thị)
    logContainer.value.scrollTop = 0;
  }
});

// Theo dõi thay đổi của metadataLogs để cuộn lên đầu khi có log mới
watch(metadataLogs, () => {
  // Đảm bảo metadataLogContainer đã được render
  if (metadataLogContainer.value) {
    // Cuộn lên đầu (vì chúng ta đã đảo ngược thứ tự hiển thị)
    metadataLogContainer.value.scrollTop = 0;
  }
});

// Đăng ký các event listeners
onMounted(() => {
  // Kiểm tra xem electronAPI có tồn tại không
  if (!window.electronAPI) {
    console.error('electronAPI không khả dụng, không thể đăng ký các sự kiện');
    return;
  }

  try {
    // Lắng nghe sự kiện tiến trình
    unsubscribeProgress = window.electronAPI.onFinetuneProgress(({progress}) => {
      finetuneProgress.value = progress;
    });

    // Lắng nghe sự kiện log
    unsubscribeLog = window.electronAPI.onFinetuneLog(({log}) => {
      try {
        const parsedLog = JSON.parse(log);
        finetuneLogs.value.push(parsedLog.message || log);
      } catch {
        // Kiểm tra xem log có phải là từ tqdm hoặc các thông báo tiến trình không
        if (log.includes('%') || log.includes('|') || log.includes('MiB/s') ||
            log.includes('iB/s') || log.includes('[A')) {
          // Đây là log tiến trình, không cần hiển thị
          return;
        }
        finetuneLogs.value.push(log);
      }
    });

    // Lắng nghe sự kiện hoàn thành
    unsubscribeComplete = window.electronAPI.onFinetuneComplete(({result}) => {
      finetuneStatus.value = 'completed';
      finetuneLogs.value.push('Quá trình finetune đã hoàn tất thành công!');

      // Thêm voice mới vào danh sách
      if (result && result.voice_name) {
        const newVoice: Voice = {
          id: uuidv4(),
          name: result.voice_name,
          createdAt: new Date().toISOString(),
          languages: ['en'],
          modelPath: result.model_path,
          configPath: result.config_path,
          vocabPath: result.vocab_path,
          referencePaths: result.reference_path || [],
          description: `Voice được tạo từ ${formData.value.inputFolder}`,
          temperature: 0.5,
          repetitionPenalty: 1.0,
          topK: 30,
          topP: 0.7,
          lengthPenalty: 1.0,
          speed: 1.0,
          type: 'finetuned'
        };

        voices.value.push(newVoice);
      }
    });

    // Lắng nghe sự kiện lỗi
    unsubscribeError = window.electronAPI.onFinetuneError(({error}) => {
      // Kiểm tra xem có phải là lỗi thực sự hay chỉ là log tiến trình
      if (error.includes('%') || error.includes('|') || error.includes('MiB/s') ||
          error.includes('iB/s') || error.includes('[A')) {
        // Đây là log tiến trình, không phải lỗi thực sự
        return;
      }

      // Đây là lỗi thực sự
      finetuneStatus.value = 'failed';
      finetuneLogs.value.push(`Lỗi: ${error}`);
    });

  } catch (error) {
    console.error('Lỗi khi đăng ký các sự kiện:', error);
  }
});

// Hủy đăng ký khi component unmount
onUnmounted(() => {
  try {
    // Hủy đăng ký các sự kiện finetune
    if (unsubscribeProgress) unsubscribeProgress();
    if (unsubscribeLog) unsubscribeLog();
    if (unsubscribeComplete) unsubscribeComplete();
    if (unsubscribeError) unsubscribeError();

    console.log('Hủy đăng ký tất cả các sự kiện thành công');
  } catch (error) {
    console.error('Lỗi khi hủy đăng ký các sự kiện:', error);
  }
});
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">{{ t('voices.finetune.title') }}</h2>

    <form @submit.prevent="startFineTune">
      <div class="mb-4">
        <label for="voiceName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{
            t('voices.finetune.form.voiceName.label')
          }}</label>
        <input
            id="voiceName"
            v-model="formData.voiceName"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            :placeholder="t('voices.finetune.form.voiceName.placeholder')"
            required
        />
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ t('voices.finetune.form.inputFolder.label') }}</label>
        <div class="flex">
          <input
              v-model="formData.inputFolder"
              type="text"
              class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              :placeholder="t('voices.finetune.form.inputFolder.placeholder')"
              readonly
              required
          />
          <button
              type="button"
              @click="selectFolder"
              class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-r-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {{ t('voices.finetune.form.inputFolder.select') }}
          </button>
        </div>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {{ t('voices.finetune.form.inputFolder.description') }}
        </p>
        <div class="mt-2 flex flex-wrap gap-2">
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <span class="font-medium">{{ t('voices.finetune.form.duration.good') }}</span>
          </span>
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            <span class="font-medium">{{ t('voices.finetune.form.duration.acceptable') }}</span>
          </span>
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            <span class="font-medium">{{ t('voices.finetune.form.duration.notRecommended') }}</span>
          </span>
        </div>
      </div>

      <!-- Hiển thị loading table khi đang xử lý metadata -->
      <div v-if="metadataProcessing && metadataItems.length > 0" class="mt-6">
        <h3 class="text-lg font-medium mb-2 text-gray-800 dark:text-white">
          {{ t('voices.finetune.metadata.processing') }}
          <span class="inline-block ml-2">
            <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
        </h3>

        <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-y-auto mb-4 h-[400px]">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ t('voices.finetune.metadata.table.file') }}
              </th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ t('voices.finetune.metadata.table.duration') }}
              </th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ t('voices.finetune.metadata.table.text') }}
              </th>
            </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
            <tr v-for="(item, index) in metadataItems" :key="index" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{{ item.fileName || 'Unknown' }}</td>
              <td class="px-4 py-2 text-sm">
                  <span
                      class="px-2 py-1 rounded-full text-xs"
                      :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': item.duration >= 3 && item.duration <= 30,
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': item.duration < 3 || (item.duration > 30 && item.duration <= 60),
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': item.duration > 60
                    }"
                  >
                    {{ item.duration }}s
                  </span>
              </td>
              <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                {{ item.text && item.text.length > 50 ? item.text.substring(0, 50) + '...' : (item.text || 'No text') }}
              </td>
            </tr>
            </tbody>
          </table>
        </div>

        <div class="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
          <div>{{ t('voices.finetune.metadata.stats.totalFiles') }} <span class="font-medium">{{ metadataItems.length }}</span> file</div>
          <div>
            <button
                @click="cancelMetadataProcessing"
                class="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm"
            >
              {{ t('voices.finetune.metadata.buttons.cancel') }}
            </button>
          </div>
        </div>

        <!-- Hiển thị logs -->
        <div ref="metadataLogContainer"
             class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 h-48 overflow-y-auto bg-gray-100 dark:bg-gray-900 font-mono text-sm">
          <div v-if="metadataLogs.length === 0" class="text-gray-500 dark:text-gray-400">
            {{ t('common.loading') }}
          </div>
          <div v-else class="flex flex-col-reverse">
            <div v-for="(log, index) in metadataLogs" :key="index" class="mb-1 py-1 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
              <span class="text-gray-800 dark:text-gray-200">{{ log }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Hiển thị metadata nếu đã xử lý -->
      <div v-if="metadataProcessed" class="mt-6">
        <h3 class="text-lg font-medium mb-2 text-gray-800 dark:text-white">{{ t('voices.finetune.metadata.processed') }}</h3>
        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('voices.finetune.metadata.stats.totalFiles') }}</p>
              <p class="text-lg font-semibold">{{ metadataStats.totalFiles }} file</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('voices.finetune.metadata.stats.totalDuration') }}</p>
              <p class="text-lg font-semibold">{{ metadataStats.totalDuration }}s</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('voices.finetune.metadata.stats.trainCount') }}</p>
              <p class="text-lg font-semibold">{{ metadataStats.trainCount }} file</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('voices.finetune.metadata.stats.evalCount') }}</p>
              <p class="text-lg font-semibold">{{ metadataStats.evalCount }} file</p>
            </div>
          </div>
        </div>

        <!-- Bảng metadata -->
        <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-y-auto mb-4 h-[400px]">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ t('voices.finetune.metadata.table.file') }}
              </th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ t('voices.finetune.metadata.table.duration') }}
              </th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ t('voices.finetune.metadata.table.text') }}
              </th>
            </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
            <tr v-for="(item, index) in metadataItems" :key="index" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{{ item.fileName }}</td>
              <td class="px-4 py-2 text-sm">
                  <span
                      class="px-2 py-1 rounded-full text-xs"
                      :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': item.duration >= 3 && item.duration <= 30,
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': item.duration < 3 || (item.duration > 30 && item.duration <= 60),
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': item.duration > 60
                    }"
                  >
                    {{ item.duration }}s
                  </span>
              </td>
              <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                {{ item.text.length > 50 ? item.text.substring(0, 50) + '...' : item.text }}
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="flex justify-end space-x-4">
        <button
            v-if="!metadataProcessing && !metadataProcessed"
            type="button"
            @click="processMetadata"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          {{ t('voices.finetune.metadata.buttons.process') }}
        </button>

        <button
            v-if="metadataProcessed"
            type="submit"
            class="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
        >
          {{ t('voices.finetune.metadata.buttons.startTraining') }}
        </button>
      </div>
    </form>

    <!-- Phần hiển thị tiến trình -->
    <div class="mt-6" v-if="finetuneStatus !== 'idle'">
      <h3 class="text-lg font-medium mb-2 text-gray-800 dark:text-white">{{ t('voices.finetune.training.title') }}</h3>

      <div class="mb-4">
        <div class="flex justify-between mb-1">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('voices.finetune.training.progress', {progress: Math.round(finetuneProgress)})
            }}</span>
          <span class="text-sm font-medium" :class="{
            'text-blue-500': finetuneStatus === 'processing',
            'text-green-500': finetuneStatus === 'completed',
            'text-red-500': finetuneStatus === 'failed'
          }">
            {{
              finetuneStatus === 'processing' ? t('voices.finetune.training.status.processing') :
                  finetuneStatus === 'completed' ? t('voices.finetune.training.status.completed') : t('voices.finetune.training.status.failed')
            }}
          </span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
          <div class="h-2.5 rounded-full"
               :class="{
                 'bg-blue-500': finetuneStatus === 'processing',
                 'bg-green-500': finetuneStatus === 'completed',
                 'bg-red-500': finetuneStatus === 'failed'
               }"
               :style="{ width: `${finetuneProgress}%` }"></div>
        </div>
      </div>

      <div class="flex justify-end mb-2" v-if="finetuneStatus === 'processing'">
        <button
            @click="showCancelConfirmationModal"
            class="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm"
        >
          {{ t('voices.finetune.training.buttons.cancel') }}
        </button>
      </div>

      <!-- Hiển thị logs -->
      <div ref="logContainer"
           class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 h-64 overflow-y-auto bg-gray-100 dark:bg-gray-900 font-mono text-sm">
        <div v-if="finetuneLogs.length === 0" class="text-gray-500 dark:text-gray-400">
          {{ t('common.loading') }}
        </div>
        <div v-else class="flex flex-col-reverse">
          <div v-for="(log, index) in finetuneLogs" :key="index" class="mb-1 py-1 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
            <span class="text-gray-800 dark:text-gray-200">{{ log }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Hiển thị khi chưa có tiến trình -->
    <div v-else class="mt-8">
      <h3 class="text-lg font-medium mb-2 text-gray-800 dark:text-white">{{ t('voices.finetune.training.title') }}</h3>
      <div
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 h-64 overflow-y-auto bg-gray-100 dark:bg-gray-900 font-mono text-sm flex items-center justify-center">
        <p class="text-gray-500 dark:text-gray-400">{{ t('voices.finetune.training.empty') }}</p>
      </div>
    </div>
  </div>

  <!-- Modal xác nhận hủy huấn luyện -->
  <div v-if="showCancelConfirmation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full">
      <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-white">{{ t('voices.finetune.confirmCancel.title') }}</h3>
      <p class="mb-6 text-gray-600 dark:text-gray-300">
        {{ t('voices.finetune.confirmCancel.message') }}
      </p>
      <div class="flex justify-end space-x-3">
        <button
            @click="closeCancelConfirmation"
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"
        >
          {{ t('voices.finetune.confirmCancel.buttons.cancel') }}
        </button>
        <button
            @click="confirmCancelFineTune"
            class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          {{ t('voices.finetune.confirmCancel.buttons.confirm') }}
        </button>
      </div>
    </div>
  </div>
</template> 