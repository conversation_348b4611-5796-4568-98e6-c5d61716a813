<script setup lang="ts">
import {ref, onMounted, watch, computed} from 'vue';
import {useI18n} from 'vue-i18n';
import {toRaw} from 'vue';
import type {Voice, TTSProcess} from '@types';
import Tooltip from '../common/Tooltip.vue';
import {useToast} from '@renderer/composables/useToast';
import {useAudioPlayer} from '@renderer/composables/useAudioPlayer';
import {useProcesses} from '@renderer/composables/useProcesses';

const {t} = useI18n();
const {showToast} = useToast();
const {audioState, toggleAudio} = useAudioPlayer();
const {createProcess} = useProcesses();

// State
const voices = ref<Voice[]>([]);
const selectedVoice = ref<Voice | null>(null);
const textInput = ref('');
const fileName = ref('');
const outputPath = ref('');
const selectedReferenceFiles = ref<string[]>([]);
const showAdvancedOptions = ref(false);
const isProcessing = ref(false);
const isPreviewPlaying = ref(false);
const numThreads = ref(2);
const numWorkers = ref(2);
const minTextLength = ref(30);

// Computed properties
const characterCount = computed(() => {
  return textInput.value.trim().length;
});

const characterCountDisplay = computed(() => {
  const count = characterCount.value;
  if (count === 0) {
    return t('home.form.text.characterCount.empty');
  }
  return t('home.form.text.characterCount.count', { count });
});

const characterCountClass = computed(() => {
  const count = characterCount.value;
  if (count === 0) {
    return 'text-gray-500 dark:text-gray-400';
  } else if (count < minTextLength.value) {
    return 'text-yellow-600 dark:text-yellow-400';
  } else if (count > 1000) {
    return 'text-orange-600 dark:text-orange-400';
  } else {
    return 'text-green-600 dark:text-green-400';
  }
});

// Methods
const loadVoices = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    const loadedVoices = await window.electronAPI.getVoices();
    // Đảm bảo các thuộc tính số có giá trị mặc định
    voices.value = loadedVoices.map(voice => {
      const defaultValues = {
        speed: 1.0,
        temperature: 0.5,
        repetitionPenalty: 1.0,
        topK: 25,
        topP: 0.65,
        lengthPenalty: 1.0
      };
      return {
        ...voice,
        speed: voice.speed || defaultValues.speed,
        temperature: voice.temperature || defaultValues.temperature,
        repetitionPenalty: voice.repetitionPenalty || defaultValues.repetitionPenalty,
        topK: voice.topK || defaultValues.topK,
        topP: voice.topP || defaultValues.topP,
        lengthPenalty: voice.lengthPenalty || defaultValues.lengthPenalty
      };
    });
    if (voices.value.length > 0 && !selectedVoice.value) {
      const defaultVoice = await window.electronAPI.getDefaultVoice();
      // Find the default voice in the newly loaded voices array
      const foundDefaultVoice = voices.value.find(v => v.id === defaultVoice?.id);
      selectedVoice.value = foundDefaultVoice || voices.value[0]; // Use found voice or the first one
    }

    // Lấy đường dẫn output mặc định
    const defaultOutputPath = await window.electronAPI.getStoreValue('outputPath');
    if (defaultOutputPath) {
      outputPath.value = defaultOutputPath;
    }
  } catch (error) {
    console.error('Lỗi khi load danh sách voices:', error);
    showToast('error', 'Không thể tải danh sách giọng nói');
  }
};

const updateVoiceProperty = (property: keyof Voice, value: string) => {
  if (!selectedVoice.value) return;

  try {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;
    const voiceId = typeof selectedVoice.value === 'string' ? selectedVoice.value : selectedVoice.value.id;

    // Find the corresponding voice object in the voices array
    const voiceInVoices = voices.value.find(v => v.id === voiceId);

    const defaultValues: Record<string, number> = {
      speed: 1.0,
      temperature: 0.4,
      repetitionPenalty: 4.0,
      topK: 25,
      topP: 0.65,
      lengthPenalty: 1.0
    };

    if (voiceInVoices) {
      // Update the property directly on the object within the voices array
      (voiceInVoices as any)[property] = numericValue || defaultValues[property] || 0;

      // Reassign selectedVoice to the same object reference from the voices array
      selectedVoice.value = voiceInVoices;
    } else {
      console.error('Không tìm thấy voice trong danh sách');
      showToast('error', 'Lỗi khi cập nhật voice');
    }

  } catch (error) {
    console.error('Lỗi khi cập nhật voice:', error);
    showToast('error', 'Lỗi khi cập nhật voice');
  }
};

const selectOutputFolder = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    const selectedPath = await window.electronAPI.selectFolder();
    if (selectedPath) {
      outputPath.value = selectedPath;
      await window.electronAPI.setStoreValue('outputPath', selectedPath);
    }
  } catch (error) {
    console.error('Lỗi khi chọn folder output:', error);
    window.electronAPI.showToast('error', 'Lỗi khi chọn folder output');
  }
};

const selectReferenceFiles = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    const selectedFiles = await window.electronAPI.selectFiles({
      filters: [
        {name: 'Audio Files', extensions: ['wav', 'mp3', 'ogg', 'm4a']}
      ],
      properties: ['openFile', 'multiSelections'],
      maxFiles: 5
    });

    if (selectedFiles && selectedFiles.length > 0) {
      selectedReferenceFiles.value = selectedFiles;
    }
  } catch (error) {
    console.error('Lỗi khi chọn file âm thanh:', error);
    showToast('error', 'Lỗi khi chọn file âm thanh');
  }
};

const removeReferenceFile = (index: number) => {
  selectedReferenceFiles.value = selectedReferenceFiles.value.filter((_, i) => i !== index);
};

const previewVoice = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    const voice = toRaw(selectedVoice.value);
    if (!voice) {
      console.error('Không tìm thấy voice được chọn');
      return;
    }

    // Nếu có previewPath, sử dụng nó
    if (voice.previewPath) {
      await toggleAudio(voice.previewPath, () => {
        isPreviewPlaying.value = false;
      });
      isPreviewPlaying.value = audioState.value.isPlaying;
    } else {
      window.electronAPI.showToast('warning', 'Không có file preview cho voice này');
    }
  } catch (error) {
    console.error('Lỗi khi preview voice:', error);
    window.electronAPI.showToast('error', 'Lỗi khi phát preview');
    isPreviewPlaying.value = false;
  }
};

const handleSubmit = async () => {
  try {
    if (!selectedVoice.value) {
      showToast('error', t('home.form.voice.required'));
      return;
    }

    if (!outputPath.value) {
      showToast('error', t('home.form.outputFolder.required'));
      return;
    }

    isProcessing.value = true;

    // Lấy object gốc trước khi clone
    const rawVoice = toRaw(selectedVoice.value);

    // Nếu có file tham chiếu được chọn, sử dụng chúng thay thế
    if (selectedReferenceFiles.value.length > 0) {
      rawVoice.referencePaths = toRaw(selectedReferenceFiles.value);
    }

    // Cập nhật voice vào store trước khi thực hiện TTS
    await window.electronAPI.updateVoice(rawVoice);
    showToast('success', t('home.form.voice.saved'));

    // Tạo tiến trình mới
    const processId = crypto.randomUUID();
    const newProcess: TTSProcess = {
      id: processId,
      text: textInput.value,
      voice: selectedVoice.value,
      status: 'pending',
      progress: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      outputPath: outputPath.value,
      fileName: fileName.value || defaultFileName(processId),
      numThreads: numThreads.value,
      numWorkers: numWorkers.value,
      chunks: []
    };

    createProcess(newProcess);

    // Reset form
    textInput.value = '';
    fileName.value = '';
  } catch (error) {
    console.error('Error submitting form:', error);
    showToast('error', String(error));
  } finally {
    isProcessing.value = false;
  }
};

const getShortId = (id: string) => id.substring(0, 8);

// Hàm format processId với timestamp và shortId
const defaultFileName = (processId: string) => {
  const now = new Date();
  const timestamp = now.getFullYear().toString().slice(-2) +
      (now.getMonth() + 1).toString().padStart(2, '0') +
      now.getDate().toString().padStart(2, '0') + '-' +
      now.getHours().toString().padStart(2, '0') +
      now.getMinutes().toString().padStart(2, '0') +
      now.getSeconds().toString().padStart(2, '0');
  return `${timestamp}-${getShortId(processId)}`;
};

// Lifecycle hooks
onMounted(async () => {
  await loadVoices();
});

watch(selectedVoice, () => {
  updateDefaultVoice();
});

const updateDefaultVoice = async () => {
  if (!selectedVoice.value) return;
  try {
    await window.electronAPI.setDefaultVoice(selectedVoice.value.id);
  } catch (error) {
    console.error('Lỗi khi cập nhật defaultVoice:', error);
  }
};
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <form @submit.prevent="handleSubmit">

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ t('home.form.outputFolder.label') }}</label>
        <div class="flex">
          <input
              type="text"
              v-model="outputPath"
              class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              :placeholder="t('home.form.outputFolder.placeholder')"
              readonly
          />
          <button
              type="button"
              @click="selectOutputFolder"
              class="px-4 py-2 bg-gray-500 text-white rounded-r-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V6z" clip-rule="evenodd"/>
            </svg>
            {{ t('home.form.outputFolder.select') }}
          </button>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 max-w-4xl">
        <div class="max-w-md w-full">
          <label for="voice" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                      d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z"
                      clip-rule="evenodd"/>
              </svg>
              {{ t('home.form.voice.label') }}
            </div>
          </label>
          <div class="flex">
            <select
                id="voice"
                v-model="selectedVoice"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
            >
              <option :value="null" disabled>{{ t('home.form.voice.placeholder') }}</option>
              <option v-for="voice in voices" :key="voice.id" :value="voice">{{ voice.name }}</option>
            </select>
          </div>
        </div>

        <div class="max-w-md w-full">
          <label for="fileName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('home.form.fileName.label') }}
          </label>
          <input
              id="fileName"
              v-model="fileName"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              :placeholder="t('home.form.fileName.placeholder')"
          />
        </div>
      </div>

      <!-- Advanced Options -->
      <div class="mb-4">
        <button
            type="button"
            @click="showAdvancedOptions = !showAdvancedOptions"
            class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        >
          <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              :class="{ 'transform rotate-90': showAdvancedOptions }"
              viewBox="0 0 20 20"
              fill="currentColor"
          >
            <path
                fill-rule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                clip-rule="evenodd"
            />
          </svg>
          {{ t('home.form.advancedOptions.label') }}
        </button>

        <div v-if="showAdvancedOptions" class="mt-4 space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Speed -->
            <div class="max-w-md w-full">
              <label for="speed" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                {{ t('home.form.speed.label', {speed: selectedVoice?.speed || 1.0}) }}
                <Tooltip>
                  <template #trigger>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-400 cursor-pointer" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"/>
                    </svg>
                  </template>
                  <div>
                    <div class="font-semibold mb-1">{{ t('home.form.speed.tooltipTitle') }}</div>
                    <div>{{ t('home.form.speed.tooltip') }}</div>
                  </div>
                </Tooltip>
              </label>
              <input
                  id="speed"
                  :value="selectedVoice?.speed || 1.0"
                  type="range"
                  min="0.5"
                  max="2"
                  step="0.1"
                  class="w-full"
                  @input="(e) => updateVoiceProperty('speed', (e.target as HTMLInputElement).value)"
              />
            </div>

            <!-- Temperature -->
            <div class="max-w-md w-full">
              <label for="temperature" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                {{ t('home.form.temperature.label', {value: selectedVoice?.temperature || 0.5}) }}
                <Tooltip>
                  <template #trigger>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-400 cursor-pointer" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"/>
                    </svg>
                  </template>
                  <div>
                    <div class="font-semibold mb-1">{{ t('home.form.temperature.tooltipTitle') }}</div>
                    <div>{{ t('home.form.temperature.tooltip') }}</div>
                  </div>
                </Tooltip>
              </label>
              <input
                  id="temperature"
                  :value="selectedVoice?.temperature || 0.5"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  class="w-full"
                  @input="(e) => updateVoiceProperty('temperature', (e.target as HTMLInputElement).value)"
              />
            </div>

            <!-- Repetition Penalty -->
            <div class="max-w-md w-full">
              <label for="repetitionPenalty" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                {{ t('home.form.repetitionPenalty.label', {value: selectedVoice?.repetitionPenalty || 1.0}) }}
                <Tooltip>
                  <template #trigger>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-400 cursor-pointer" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"/>
                    </svg>
                  </template>
                  <div>
                    <div class="font-semibold mb-1">{{ t('home.form.repetitionPenalty.tooltipTitle') }}</div>
                    <div>{{ t('home.form.repetitionPenalty.tooltip') }}</div>
                  </div>
                </Tooltip>
              </label>
              <input
                  id="repetitionPenalty"
                  :value="selectedVoice?.repetitionPenalty || 1.0"
                  type="range"
                  min="1"
                  max="5"
                  step="0.1"
                  class="w-full"
                  @input="(e) => updateVoiceProperty('repetitionPenalty', (e.target as HTMLInputElement).value)"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Number of Threads -->
            <div class="max-w-md w-full">
              <label for="numThreads" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                {{ t('home.form.numThreads.label', {value: numThreads}) }}
                <Tooltip>
                  <template #trigger>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-400 cursor-pointer" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"/>
                    </svg>
                  </template>
                  <div>
                    <div class="font-semibold mb-1">{{ t('home.form.numThreads.tooltipTitle') }}</div>
                    <div>{{ t('home.form.numThreads.tooltip') }}</div>
                  </div>
                </Tooltip>
              </label>
              <input
                  id="numThreads"
                  v-model="numThreads"
                  type="number"
                  min="1"
                  max="16"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <!-- Number of Workers -->
            <div class="max-w-md w-full">
              <label for="numWorkers" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                {{ t('home.form.numWorkers.label', {value: numWorkers}) }}
                <Tooltip>
                  <template #trigger>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-400 cursor-pointer" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"/>
                    </svg>
                  </template>
                  <div>
                    <div class="font-semibold mb-1">{{ t('home.form.numWorkers.tooltipTitle') }}</div>
                    <div>{{ t('home.form.numWorkers.tooltip') }}</div>
                  </div>
                </Tooltip>
              </label>
              <input
                  id="numWorkers"
                  v-model="numWorkers"
                  type="number"
                  min="1"
                  max="16"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ t('home.form.referenceFiles.label') }}</label>
            <div class="flex flex-col gap-2">
              <div class="flex">
                <input
                    type="text"
                    :value="selectedReferenceFiles.length > 0 ? `${selectedReferenceFiles.length} files selected` : t('home.form.referenceFiles.placeholder')"
                    class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    readonly
                />
                <button
                    type="button"
                    @click="selectReferenceFiles"
                    class="px-4 py-2 bg-gray-500 text-white rounded-r-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                          clip-rule="evenodd"/>
                  </svg>
                  {{ t('home.form.referenceFiles.select') }}
                </button>
              </div>
              <div v-if="selectedReferenceFiles.length > 0" class="flex flex-wrap gap-2">
                <div v-for="(file, index) in selectedReferenceFiles" :key="index"
                     class="flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                  <span class="text-sm truncate max-w-xs">{{ file.split('/').pop() }}</span>
                  <button @click="removeReferenceFile(index)" class="text-red-500 hover:text-red-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-4">
        <label for="text" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ t('home.form.text.label') }}</label>
        <textarea
            id="text"
            v-model="textInput"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            :placeholder="t('home.form.text.placeholder')"
            :minlength="minTextLength"
            required
        ></textarea>
        <div class="mt-2 flex justify-between items-center">
          <div :class="characterCountClass" class="text-sm font-medium">
            {{ characterCountDisplay }}
          </div>
          <div v-if="characterCount > 0 && characterCount < minTextLength " class="text-sm text-yellow-600 dark:text-yellow-400">
            {{ t('home.form.text.characterCount.minimum', { minimum: minTextLength }) }}
          </div>
        </div>
      </div>

      <div class="flex justify-end space-x-2">
        <button
            type="button"
            @click="previewVoice"
            class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center"
            :disabled="!selectedVoice"
        >
          <svg v-if="!isPreviewPlaying" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                  clip-rule="evenodd"/>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                  clip-rule="evenodd"/>
          </svg>
          {{ isPreviewPlaying ? t('home.form.buttons.stop') : t('home.form.buttons.preview') }}
        </button>
        <button
            type="submit"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
            :disabled="isProcessing"
        >
          <svg v-if="isProcessing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
               viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isProcessing ? t('home.form.buttons.processing') : t('home.form.buttons.convert') }}
        </button>
      </div>
    </form>
  </div>
</template>