<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { logger } from '../utils/logger';
import type { LogLevel, LogChannel, LogSource } from '@types';

// Props
const props = defineProps({
  channel: {
    type: String,
    default: null
  },
  level: {
    type: String,
    default: null
  },
  processId: {
    type: String,
    default: null
  },
  maxItems: {
    type: Number,
    default: 100
  },
  autoScroll: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: 'Logs'
  },
  showControls: {
    type: Boolean,
    default: true
  },
  height: {
    type: String,
    default: '400px'
  }
});

// State
const logs = ref<any[]>([]);
const filter = ref<{
  level: LogLevel | null;
  channel: LogChannel | null;
  source?: LogSource;
  processId?: string | null;
  startTime?: string | null;
  endTime?: string | null;
}>({
  level: props.level as LogLevel | null,
  channel: props.channel as LogChannel | null,
  processId: props.processId,
  startTime: null,
  endTime: null
});
const loading = ref(false);
const unsubscribe = ref<(() => void) | null>(null);

// Computed
const filteredLogs = computed(() => {
  let result = [...logs.value];

  if (filter.value.level) {
    result = result.filter(log => log.level === filter.value.level);
  }

  if (filter.value.channel) {
    result = result.filter(log => log.channel === filter.value.channel);
  }

  if (filter.value.processId) {
    result = result.filter(log => log.processId === filter.value.processId);
  }

  if (filter.value.startTime) {
    result = result.filter(log => log.timestamp >= filter.value.startTime!);
  }

  if (filter.value.endTime) {
    result = result.filter(log => log.timestamp <= filter.value.endTime!);
  }

  // Giới hạn số lượng logs hiển thị
  if (result.length > props.maxItems) {
    result = result.slice(result.length - props.maxItems);
  }

  // Đảo ngược thứ tự để hiển thị log mới nhất ở trên đầu
  return result.reverse();
});

// Methods
const loadLogs = async () => {
  loading.value = true;
  try {
    // Tạo một bản sao của filter và loại bỏ các giá trị null
    const filterParams: any = {};

    if (filter.value.level) filterParams.level = filter.value.level;
    if (filter.value.channel) filterParams.channel = filter.value.channel;
    if (filter.value.source) filterParams.source = filter.value.source;
    if (filter.value.processId) filterParams.processId = filter.value.processId;
    if (filter.value.startTime) filterParams.startTime = filter.value.startTime;
    if (filter.value.endTime) filterParams.endTime = filter.value.endTime;

    const result = await logger.getLogs(Object.keys(filterParams).length > 0 ? filterParams : undefined);
    logs.value = result;
  } catch (error) {
    console.error('Lỗi khi tải logs:', error);
  } finally {
    loading.value = false;
  }
};

const clearLogs = async () => {
  try {
    await logger.clearLogs();
    logs.value = [];
  } catch (error) {
    console.error('Lỗi khi xóa logs:', error);
  }
};

const refreshLogs = () => {
  loadLogs();
};

const handleNewLog = (log: any) => {
  // Kiểm tra xem log có phù hợp với filter không
  let shouldAdd = true;

  if (filter.value.level && log.level !== filter.value.level) {
    shouldAdd = false;
  }

  if (filter.value.channel && log.channel !== filter.value.channel) {
    shouldAdd = false;
  }

  if (filter.value.processId && log.processId !== filter.value.processId) {
    shouldAdd = false;
  }

  if (filter.value.startTime && log.timestamp < filter.value.startTime) {
    shouldAdd = false;
  }

  if (filter.value.endTime && log.timestamp > filter.value.endTime) {
    shouldAdd = false;
  }

  if (shouldAdd) {
    // Thêm log mới vào đầu mảng (vì chúng ta đã đảo ngược thứ tự hiển thị)
    logs.value.unshift(log);

    // Giới hạn số lượng logs
    if (logs.value.length > props.maxItems * 2) {
      logs.value = logs.value.slice(0, props.maxItems);
    }

    // Auto scroll (vì log mới ở trên đầu nên cần scroll lên đầu)
    if (props.autoScroll) {
      setTimeout(() => {
        const logContainer = document.querySelector('.log-container');
        if (logContainer) {
          logContainer.scrollTop = 0;
        }
      }, 0);
    }
  }
};

// Lifecycle hooks
onMounted(async () => {
  // Tải logs ban đầu
  await loadLogs();

  // Đăng ký lắng nghe log mới
  unsubscribe.value = logger.onNewLog(handleNewLog);
});

onUnmounted(() => {
  // Hủy đăng ký lắng nghe log mới
  if (unsubscribe.value) {
    unsubscribe.value();
  }
});

// Watch changes
watch(() => props.channel, (newValue) => {
  filter.value.channel = newValue as LogChannel | null;
  loadLogs();
});

watch(() => props.level, (newValue) => {
  filter.value.level = newValue as LogLevel | null;
  loadLogs();
});

watch(() => props.processId, (newValue) => {
  filter.value.processId = newValue;
  loadLogs();
});

// Helpers
const getLogClass = (level: string) => {
  switch (level) {
    case 'debug':
      return 'text-gray-500 dark:text-gray-400';
    case 'info':
      return 'text-blue-500 dark:text-blue-400';
    case 'warning':
      return 'text-yellow-500 dark:text-yellow-400';
    case 'error':
      return 'text-red-500 dark:text-red-400';
    default:
      return '';
  }
};

const formatTime = (timestamp: string) => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  } catch (e) {
    return timestamp;
  }
};

const formatData = (process: any) => {
  if (!process) return '';
  try {
    return JSON.stringify(process, null, 2);
  } catch (e) {
    return String(process);
  }
};
</script>

<template>
  <div class="log-viewer">
    <div class="flex justify-between items-center mb-2">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white">{{ title }}</h3>
      <div v-if="showControls" class="flex space-x-2">
        <button
          @click="refreshLogs"
          class="px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
        >
          Làm mới
        </button>
        <button
          @click="clearLogs"
          class="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
        >
          Xóa logs
        </button>
      </div>
    </div>

    <div v-if="showControls" class="flex flex-wrap gap-2 mb-2">
      <select
        v-model="filter.level"
        class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
        @change="loadLogs"
      >
        <option :value="null">Tất cả levels</option>
        <option value="debug">Debug</option>
        <option value="info">Info</option>
        <option value="warning">Warning</option>
        <option value="error">Error</option>
      </select>

      <select
        v-model="filter.channel"
        class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
        @change="loadLogs"
      >
        <option :value="null">Tất cả channels</option>
        <option value="system">System</option>
        <option value="metadata">Metadata</option>
        <option value="finetune">Finetune</option>
        <option value="tts">TTS</option>
        <option value="general">General</option>
      </select>
    </div>

    <div
      class="log-container border border-gray-200 dark:border-gray-700 rounded p-2 overflow-y-auto font-mono text-sm bg-white dark:bg-gray-800"
      :style="{ height: height }"
    >
      <div v-if="loading" class="flex justify-center items-center h-full">
        <span class="text-gray-500 dark:text-gray-400">Đang tải logs...</span>
      </div>

      <div v-else-if="filteredLogs.length === 0" class="flex justify-center items-center h-full">
        <span class="text-gray-500 dark:text-gray-400">Không có logs nào</span>
      </div>

      <template v-else>
        <div
          v-for="(log, index) in filteredLogs"
          :key="index"
          class="log-item py-1 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
        >
          <div class="flex">
            <span class="text-gray-500 dark:text-gray-400 mr-2">{{ formatTime(log.timestamp) }}</span>
            <span :class="getLogClass(log.level)" class="font-semibold mr-2">[{{ log.level.toUpperCase() }}]</span>
            <span class="text-purple-500 dark:text-purple-400 mr-2">[{{ log.channel }}]</span>
            <span class="text-gray-700 dark:text-gray-300 flex-grow">{{ log.message }}</span>
          </div>

          <div v-if="log.process" class="mt-1 pl-4 text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
            {{ formatData(log.process) }}
          </div>

          <div v-if="log.processId" class="mt-1 pl-4 text-xs text-gray-500 dark:text-gray-400">
            Process ID: {{ log.processId }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.log-container {
  scrollbar-width: thin;
}

.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.log-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  .log-container::-webkit-scrollbar-track {
    background: #2d3748;
  }

  .log-container::-webkit-scrollbar-thumb {
    background: #4a5568;
  }

  .log-container::-webkit-scrollbar-thumb:hover {
    background: #718096;
  }
}
</style>
