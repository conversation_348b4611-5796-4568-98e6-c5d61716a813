<script setup lang="ts">
import { onMounted, ref } from 'vue';
import Navbar from './components/common/Navbar.vue';
import Toast from './components/common/Toast.vue';
import { RouterView } from 'vue-router';

const theme = ref<'light' | 'dark'>('light');

onMounted(async () => {
  try {
    // Kiểm tra xem electronAPI có tồn tại không
    if (window.electronAPI) {
      // Lấy theme từ electron-store
      const savedTheme = await window.electronAPI.getStoreValue('theme') as 'light' | 'dark';
      if (savedTheme) {
        theme.value = savedTheme;
        document.documentElement.classList.add(savedTheme);
      } else {
        // Mặc định là light theme
        document.documentElement.classList.add('light');
      }
    } else {
      console.warn('electronAPI không khả dụng, sử dụng theme mặc định');
      document.documentElement.classList.add('light');
    }
  } catch (error) {
    console.error('Lỗi khi khởi tạo theme:', error);
    document.documentElement.classList.add('light');
  }
});

// Hàm chuyển đổi theme
const toggleTheme = async () => {
  const newTheme = theme.value === 'light' ? 'dark' : 'light';
  document.documentElement.classList.remove(theme.value);
  document.documentElement.classList.add(newTheme);
  theme.value = newTheme;

  // Lưu theme vào electron-store nếu có thể
  if (window.electronAPI) {
    try {
      await window.electronAPI.setStoreValue('theme', newTheme);
    } catch (error) {
      console.error('Lỗi khi lưu theme:', error);
    }
  }
};
</script>

<template>
  <div class="min-h-screen transition-colors duration-300" :class="theme">
    <Navbar :theme="theme" @toggle-theme="toggleTheme" />
    <main class="container mx-auto px-4 py-6">
      <router-view />
    </main>
    <Toast />
  </div>
</template>
