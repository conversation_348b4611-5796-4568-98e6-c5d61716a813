#!/usr/bin/env node

const {spawn} = require('child_process');
const fs = require('fs');
const path = require('path');
const {context} = require('esbuild');

// const execAsync = promisify(exec);
const rootDir = path.resolve(__dirname, '..');

// <PERSON><PERSON><PERSON> thư mục dist trước khi build trong development
if (process.env.NODE_ENV === 'development') {
  if (fs.existsSync(path.join(rootDir, 'dist'))) {
    fs.rmSync(path.join(rootDir, 'dist'), { recursive: true, force: true });
  }
}

// Xác định môi trường
const isDevelopment = process.env.NODE_ENV === 'development';

// Đảm bảo thư mục dist tồn tại
const distDir = path.join(rootDir, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, {recursive: true});
}

// Kiểm tra venv cho development
if (isDevelopment) {
  const venvPath = path.join(rootDir, '.venv');
  if (!fs.existsSync(venvPath)) {
    console.warn('⚠️  Không tìm thấy thư mục venv cho Python. Hãy chạy: npm run setup để tạo môi trường ảo Python cho development!');
  }
}

async function buildMainProcess() {
  console.log('Building main process and preload...');
  try {
    // Build main process
    const mainContext = await context({
      entryPoints: ['src/main/index.ts'],
      bundle: true,
      outfile: 'dist/index.cjs',
      platform: 'node',
      format: 'cjs',
      external: ['electron'],
      minify: false,
      sourcemap: true,
      resolveExtensions: ['.ts', '.js'],
      banner: {
        js: '// @ts-nocheck\n',
      },
      define: {
        'process.env.NODE_ENV': isDevelopment ? '"development"' : '"production"',
      },
      alias: {
        '@': path.resolve(rootDir, 'src'),
        '@main': path.resolve(rootDir, 'src/main'),
        '@renderer': path.resolve(rootDir, 'src/renderer'),
        '@preload': path.resolve(rootDir, 'src/preload'),
        '@python': path.resolve(rootDir, 'src/python'),
        '@store': path.resolve(rootDir, 'src/store'),
        '@types': path.resolve(rootDir, 'src/types'),
        '@utils': path.resolve(rootDir, 'src/utils')
      },
    });

    // Build preload script
    const preloadContext = await context({
      entryPoints: ['src/preload/index.ts'],
      bundle: true,
      outfile: 'dist/preload.js',
      platform: 'node',
      format: 'cjs',
      external: ['electron'],
      minify: false,
      sourcemap: true,
      tsconfig: 'tsconfig.preload.json',
      banner: {
        js: '// @ts-nocheck\n',
      },
      define: {
        'process.env.NODE_ENV': isDevelopment ? '"development"' : '"production"',
      },
      resolveExtensions: ['.ts', '.js'],
      alias: {
        '@': path.resolve(rootDir, 'src'),
        '@main': path.resolve(rootDir, 'src/main'),
        '@renderer': path.resolve(rootDir, 'src/renderer'),
        '@preload': path.resolve(rootDir, 'src/preload'),
        '@python': path.resolve(rootDir, 'src/python'),
        '@store': path.resolve(rootDir, 'src/store'),
        '@types': path.resolve(rootDir, 'src/types'),
        '@utils': path.resolve(rootDir, 'src/utils')
      },
    });

    // Build lần đầu
    await mainContext.rebuild();
    await preloadContext.rebuild();

    // Watch mode cho development
    if (isDevelopment) {
      await mainContext.watch();
      await preloadContext.watch();
      console.log('Watching for changes...');
    } else {
      await mainContext.dispose();
      await preloadContext.dispose();
    }

    console.log('Main process and preload built successfully!');
  } catch (error) {
    console.error('Error building main process:', error);
    process.exit(1);
  }
}

async function main() {
  // Chỉ set NODE_ENV thành development nếu chưa được đặt
  if (!process.env.NODE_ENV) {
    process.env.NODE_ENV = 'development';
  }

  await buildMainProcess();

  // Chỉ khởi động Electron trong môi trường development
  if (isDevelopment) {
    console.log('Starting Electron with Vite dev server...');
    setTimeout(() => {
      const electronPath = require('electron');
      let electronProcess = null;
      const spawnElectron = () => {
        if (electronProcess) {
          electronProcess.kill();
        }
        electronProcess = spawn(electronPath, ['.'], {
          env: {
            ...process.env,
            VITE_DEV_SERVER_URL: 'http://localhost:11001',
            NODE_ENV: 'development',
            ELECTRON_IS_DEV: '1',
          },
          stdio: 'inherit',
        });
        electronProcess.on('exit', (code) => {
          if (code !== 0) {
            console.error(`Electron process exited with code ${code}. Có thể có lỗi khi khởi động ứng dụng. Kiểm tra lại log ở trên để biết chi tiết.`);
          } else {
            console.log(`Electron process exited with code ${code}`);
          }
        });
      };
      spawnElectron();
      const chokidar = require('chokidar');
      const watcher = chokidar.watch([
        path.join(rootDir, 'dist', 'index.cjs'),
        path.join(rootDir, 'dist', 'preload.js'),
      ]);
      watcher.on('change', (filePath) => {
        console.log(`File changed: ${filePath}. Restarting Electron...`);
        spawnElectron();
      });
    }, 2000);
    // Giữ process sống trong dev mode
    await new Promise(() => {});
  }
}

main().catch((error) => {
  console.error(error);
  process.exit(1);
});
