import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function getBrewPrefix(formula) {
  if (process.platform !== 'darwin') return null;
  try {
    return execSync(`brew --prefix ${formula}`).toString().trim();
  } catch (e) {
    console.warn(`CẢNH BÁO: Không thể tìm thấy Homebrew prefix cho '${formula}'.`);
    return null;
  }
}

function getMacDynamicLibs() {
  if (process.platform !== 'darwin') return [];
  const libs = [];
  const formulasToLibs = {
    'sox': ['lib/libsox.dylib'],
    'ffmpeg': [
      'lib/libavcodec.dylib', 'lib/libavformat.dylib',
      'lib/libavutil.dylib', 'lib/libavfilter.dylib',
      'lib/libavdevice.dylib', 'bin/ffmpeg'
    ]
  };

  for (const formula in formulasToLibs) {
    const prefix = getBrewPrefix(formula);
    if (prefix) {
      formulasToLibs[formula].forEach(libFile => {
        const libPath = path.join(prefix, libFile);
        if (fs.existsSync(libPath)) {
          libs.push(`--add-binary "${libPath}:."`);
          console.log(`INFO: Sẽ thêm: ${libPath}`);
        } else {
          console.warn(`CẢNH BÁO: Không tìm thấy thư viện tại: ${libPath}.`);
        }
      });
    }
  }
  return libs;
}

function packagePythonExecutables() {
  const ROOT_DIR = path.join(__dirname, '..');
  const RESOURCES_DIR = path.join(ROOT_DIR, 'resources');
  const DIST_PYTHON_DIR = path.join(RESOURCES_DIR, 'bundled_python');
  const PYTHON_SRC_DIR = path.join(ROOT_DIR, 'src/python');
  
  // SỬA ĐỔI: Xác định đường dẫn đầy đủ đến các file thực thi trong venv
  const IS_WINDOWS = process.platform === 'win32';
  const VENV_BIN_DIR = IS_WINDOWS ? path.join(ROOT_DIR, '.venv', 'Scripts') : path.join(ROOT_DIR, '.venv', 'bin');
  const VENV_PYTHON = path.join(VENV_BIN_DIR, IS_WINDOWS ? 'python.exe' : 'python');
  const VENV_PYINSTALLER = path.join(VENV_BIN_DIR, IS_WINDOWS ? 'pyinstaller.exe' : 'pyinstaller');

  console.log('Đang build ứng dụng Python (chế độ one-folder) vào:', DIST_PYTHON_DIR);
  // ... (phần dọn dẹp thư mục giữ nguyên)
  
  const specPath = path.join(ROOT_DIR, 'pyinstaller_build', 'spec');
  const buildPath = path.join(ROOT_DIR, 'pyinstaller_build', 'build');
  const mainPythonFile = path.join(PYTHON_SRC_DIR, 'main.py');
  const appNameFromScript = path.parse(mainPythonFile).name;
  const appOutputDir = path.join(DIST_PYTHON_DIR, appNameFromScript);
  
  fs.rmSync(appOutputDir, { recursive: true, force: true });
  fs.ensureDirSync(DIST_PYTHON_DIR);

  // Lấy site-packages từ venv một cách an toàn
  const venvSitePackages = execSync(`"${VENV_PYTHON}" -c "import site; print(site.getsitepackages()[0])"`).toString().trim();
  const addDataOptions = [];
  ['trainer', 'gruut', 'gruut_lang_en'].forEach(pkg => {
      const sourcePath = path.join(venvSitePackages, pkg);
      if (fs.existsSync(sourcePath)) {
          // Sử dụng path.delimiter cho đúng HĐH
          addDataOptions.push(`--add-data "${sourcePath}${path.delimiter}${pkg}"`);
      }
  });

  const cmd = [
    // SỬA ĐỔI: Sử dụng đường dẫn tuyệt đối đến pyinstaller
    `"${VENV_PYINSTALLER}"`,
    '--noconfirm', '--clean', '--onedir',
    `--distpath "${DIST_PYTHON_DIR}"`,
    `--workpath "${buildPath}"`,
    `--specpath "${specPath}"`,
    `--paths "${PYTHON_SRC_DIR}"`,
    `--runtime-hook "${path.join(__dirname, 'runtime_hook.py')}"`,
    '--collect-all TTS', 
    '--collect-all torch', 
    '--collect-all torchaudio',
    '--collect-all numba', 
    '--collect-all whisper', 
    '--collect-all transformers',
    '--collect-all jamo',
    ...getMacDynamicLibs(),
    ...addDataOptions,
    '--exclude-module tkinter',
    `"${mainPythonFile}"`
  ].join(' ');

  console.log(`Đang thực thi lệnh PyInstaller:`);
  console.log(cmd);

  try {
    // Không cần kích hoạt venv vì chúng ta đã dùng đường dẫn tuyệt đối
    execSync(cmd, { stdio: 'inherit', cwd: ROOT_DIR, env: { 
      ...process.env, 'PYTHONOPTIMIZE': '1', 'PYTHONUTF8': '1'
    } });

    // ... (phần xử lý sau build giữ nguyên)
    const executablePath = path.join(appOutputDir, appNameFromScript + (IS_WINDOWS ? '.exe' : ''));
    console.log(`Build thành công! Output: ${appOutputDir}`);

    if (!IS_WINDOWS) {
      console.log('Fixing library paths for macOS...');
      execSync(`chmod +x "${executablePath}"`);
      // ... (phần install_name_tool giữ nguyên)
    }
    return true;
  } catch (error) {
    console.error(`Lỗi khi build main.py:`, error);
    // SỬA ĐỔI: Thoát với mã lỗi để CI báo lỗi
    process.exit(1); 
  }
}

// Chạy hàm chính
packagePythonExecutables();