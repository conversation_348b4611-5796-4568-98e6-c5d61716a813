import { execSync } from 'child_process';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// --- <PERSON><PERSON><PERSON> (Không đổi) ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.resolve(__dirname, '..');
const PYTHON_ENV_DIR = path.join(ROOT_DIR, '.venv');
const REQUIREMENTS_PATH = path.join(ROOT_DIR, 'src', 'python', 'requirements.txt');
const PYTHON_VERSION_TARGET = '3.10';
const IS_WINDOWS = process.platform === 'win32';
const VENV_PYTHON = IS_WINDOWS
  ? path.join(PYTHON_ENV_DIR, 'Scripts', 'python.exe')
  : path.join(PYTHON_ENV_DIR, 'bin', 'python');

// --- <PERSON><PERSON><PERSON> hàm tiện ích ---
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};
function log(message, color = colors.reset) { console.log(`${color}${message}${colors.reset}`); }
function runCommand(command, errorMessage) {
    try {
        execSync(command, { stdio: 'inherit' });
    } catch (error) {
        log(`❌ ${errorMessage}: ${error.message}`, colors.red);
        throw error;
    }
}

// --- Các hàm chính (ĐÃ SỬA ĐỔI) ---

function getPythonCommand() {
    // Ưu tiên các lệnh cụ thể để tránh nhầm lẫn với python hệ thống
    const commands = ['python3.10', 'python3'];
    if (IS_WINDOWS) {
        commands.unshift('python'); // Trên Windows, thường chỉ có 'python'
    }
    
    for (const cmd of commands) {
        try {
            const version = execSync(`${cmd} --version`).toString().trim();
            if (version.includes(` ${PYTHON_VERSION_TARGET}.`)) {
                log(`✅ Sử dụng lệnh '${cmd}' (phiên bản ${version}) để tạo venv.`, colors.green);
                return cmd;
            }
        } catch (e) {
            // Bỏ qua nếu lệnh không tồn tại
        }
    }
    return null; // Không tìm thấy lệnh Python 3.10 phù hợp
}

function checkAndInstallPython() {
    log('📋 Kiểm tra và cài đặt Python 3.10...');
    
    if (getPythonCommand()) {
        log('✅ Python 3.10 đã được cài đặt và có thể truy cập.', colors.green);
        return;
    }

    log(`🔄 Không tìm thấy Python ${PYTHON_VERSION_TARGET}. Sẽ thử cài đặt qua package manager.`, colors.cyan);
    if (IS_WINDOWS) {
        log('👉 Vui lòng cài đặt Python 3.10.11 thủ công cho Windows từ:', colors.yellow);
        log('   https://www.python.org/ftp/python/3.10.11/python-3.10.11-amd64.exe', colors.bright);
        log('   Khi cài đặt, nhớ chọn "Add Python 3.10 to PATH".', colors.yellow);
        throw new Error("Yêu cầu cài đặt Python thủ công trên Windows.");
    } else if (process.platform === 'darwin') {
        runCommand('brew install python@3.10', 'Lỗi khi cài đặt Python bằng Homebrew');
    } else { // Linux
        runCommand(`sudo apt-get update && sudo apt-get install -y python${PYTHON_VERSION_TARGET} python${PYTHON_VERSION_TARGET}-venv`, 'Lỗi khi cài đặt Python bằng apt');
    }

    // Kiểm tra lại sau khi cài đặt
    if (!getPythonCommand()) {
        throw new Error("Cài đặt Python 3.10 dường như đã thất bại. Không thể tìm thấy lệnh 'python3.10'.");
    }

    log('✅ Đã cài đặt Python thành công.', colors.green);
}

function createVirtualEnv() {
    // **SỬA ĐỔI QUAN TRỌNG: Luôn xóa và tạo lại venv để đảm bảo sạch sẽ**
    log('🔄 Dọn dẹp môi trường ảo cũ (nếu có) và tạo môi trường mới...', colors.cyan);
    if (fs.existsSync(PYTHON_ENV_DIR)) {
        fs.removeSync(PYTHON_ENV_DIR);
        log('   -> Đã xóa thư mục .venv cũ.');
    }
    
    const pythonCmd = getPythonCommand();
    if (!pythonCmd) {
        throw new Error("Không tìm thấy lệnh Python 3.10 phù hợp để tạo môi trường ảo.");
    }

    // **SỬA ĐỔI QUAN TRỌNG: Sử dụng lệnh python3.10 đã tìm thấy**
    runCommand(`${pythonCmd} -m venv ${PYTHON_ENV_DIR}`, 'Lỗi khi tạo môi trường ảo');
    log('✅ Đã tạo môi trường ảo mới thành công với Python 3.10.', colors.green);
}

function installPythonDependencies() {
    log('🔄 Đang cài đặt các phụ thuộc Python từ requirements.txt...', colors.cyan);
    log('   (Quá trình này có thể mất nhiều thời gian, đặc biệt là khi tải PyTorch)', colors.yellow);
    runCommand(
        `"${VENV_PYTHON}" -m pip install --upgrade pip`,
        'Lỗi khi nâng cấp pip'
    );
    runCommand(
        `"${VENV_PYTHON}" -m pip install -r "${REQUIREMENTS_PATH}"`,
        'Lỗi khi cài đặt các phụ thuộc Python'
    );
    log('✅ Đã cài đặt các phụ thuộc Python thành công.', colors.green);
}

function createEnvFile() {
    const envPath = path.join(ROOT_DIR, '.env');
    const envContent = `PYTHON_EXECUTABLE=${VENV_PYTHON.replace(/\\/g, '/')}\n`;
    fs.writeFileSync(envPath, envContent);
    log(`✅ Đã tạo file .env để lưu đường dẫn Python.`, colors.green);
}

async function main() {
    try {
        log("🚀 Bắt đầu thiết lập môi trường development cho Gen Voice\n", colors.bright + colors.cyan);

        // 1. Cài đặt các phụ thuộc hệ thống (ffmpeg, etc.)
        log('📦 Bước 1: Cài đặt các phụ thuộc hệ thống (ffmpeg, sox)...', colors.blue);
        runCommand('npm run setup-system-deps', 'Lỗi khi cài đặt các phụ thuộc hệ thống');

        // 2. Kiểm tra/Cài đặt Python
        log('\n📦 Bước 2: Kiểm tra và cài đặt Python...', colors.blue);
        checkAndInstallPython();

        // 3. Tạo môi trường ảo (Đã sửa đổi để luôn sạch)
        log('\n📦 Bước 3: Tạo môi trường ảo Python...', colors.blue);
        createVirtualEnv();

        // 4. Cài đặt các phụ thuộc Python
        log('\n📦 Bước 4: Cài đặt các phụ thuộc Python...', colors.blue);
        installPythonDependencies();
        
        // 5. Tạo file .env
        log('\n📦 Bước 5: Tạo file cấu hình môi trường...', colors.blue);
        createEnvFile();

        log('\n🎉 Thiết lập môi trường development hoàn tất!', colors.bright + colors.green);
        log('   Bây giờ bạn có thể chạy `npm run dev` để bắt đầu.', colors.cyan);

    } catch (error) {
        log(`\n❌ Quá trình thiết lập đã gặp lỗi. Vui lòng kiểm tra các thông báo ở trên.`, colors.red);
        process.exit(1);
    }
}

main();