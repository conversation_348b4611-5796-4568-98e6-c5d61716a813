#!/usr/bin/env node

/**
 * <PERSON><PERSON>t tự động cài đặt các phụ thuộc hệ thống cần thiết cho Gen Voice.
 * Bao gồm: ffmpeg, MeCab, và các phụ thuộc khác.
 */

import {execSync} from 'child_process';
import fs from 'fs-extra';

// Màu sắc cho console
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

// Kiểm tra hệ điều hành
const IS_WINDOWS = process.platform === 'win32';
const IS_MACOS = process.platform === 'darwin';
const IS_LINUX = process.platform === 'linux';

// Hàm log với màu sắc
function log(message, color = colors.reset) {
  console.log(color + message + colors.reset);
}

// Kiểm tra xem ffmpeg đã được cài đặt chưa
function checkFfmpeg() {
  try {
    const version = execSync('ffmpeg -version').toString().trim().split(
        '\n')[0];
    log(`✅ ${version}`, colors.green);
    return true;
  } catch (error) {
    log('❌ ffmpeg không được tìm thấy hoặc có lỗi', colors.red);
    return false;
  }
}

// Cài đặt ffmpeg
function installFfmpeg() {
  try {
    log('🔄 Đang cài đặt ffmpeg...', colors.cyan);

    if (IS_WINDOWS) {
      log('Vui lòng cài đặt ffmpeg thủ công từ https://ffmpeg.org/download.html',
          colors.yellow);
      log('Sau khi cài đặt, đảm bảo ffmpeg được thêm vào PATH', colors.yellow);
      return false;
    } else if (IS_MACOS) {
      // Kiểm tra xem Homebrew đã được cài đặt chưa
      try {
        execSync('brew --version', {stdio: 'ignore'});
      } catch (error) {
        log('Đang cài đặt Homebrew...', colors.cyan);
        execSync(
            '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"',
            {stdio: 'inherit'});
      }

      // Gỡ cài đặt ffmpeg@4 nếu có (có thể gây lỗi libvpx.9.dylib)
      log('Đang gỡ cài đặt ffmpeg@4 nếu có...', colors.cyan);
      try {
        execSync('brew uninstall ffmpeg@4 --force', {stdio: 'ignore'});
      } catch (error) {
        // Bỏ qua lỗi nếu không có ffmpeg@4
      }

      // Cài đặt ffmpeg phiên bản mới nhất
      log('Đang cài đặt ffmpeg phiên bản mới nhất...', colors.cyan);
      execSync('brew install ffmpeg', {stdio: 'inherit'});
    } else if (IS_LINUX) {
      // Cài đặt ffmpeg trên Linux
      if (fs.existsSync('/etc/debian_version')) {
        // Debian/Ubuntu
        log('Đang cài đặt ffmpeg trên Debian/Ubuntu...', colors.cyan);
        execSync('sudo apt update && sudo apt install -y ffmpeg',
            {stdio: 'inherit'});
      } else if (fs.existsSync('/etc/fedora-release')) {
        // Fedora
        log('Đang cài đặt ffmpeg trên Fedora...', colors.cyan);
        execSync('sudo dnf install -y ffmpeg', {stdio: 'inherit'});
      } else if (fs.existsSync('/etc/redhat-release')) {
        // CentOS/RHEL
        log('Đang cài đặt ffmpeg trên CentOS/RHEL...', colors.cyan);
        execSync(
            'sudo yum install -y epel-release && sudo yum install -y ffmpeg',
            {stdio: 'inherit'});
      } else {
        log('Không thể xác định hệ điều hành Linux. Vui lòng cài đặt ffmpeg thủ công.',
            colors.yellow);
        return false;
      }
    }

    // Kiểm tra lại
    const version = execSync('ffmpeg -version').toString().trim().split(
        '\n')[0];
    log(`✅ Đã cài đặt ffmpeg: ${version}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ Lỗi khi cài đặt ffmpeg: ${error.message}`, colors.red);
    return false;
  }
}

// Kiểm tra xem MeCab đã được cài đặt chưa
function checkMeCab() {
  try {
    const version = execSync('mecab --version').toString().trim().split(
        '\n')[0];
    log(`✅ MeCab: ${version}`, colors.green);
    return true;
  } catch (error) {
    log('❌ MeCab không được tìm thấy hoặc có lỗi', colors.red);
    return false;
  }
}

// Cài đặt MeCab
function installMeCab() {
  try {
    log('🔄 Đang cài đặt MeCab...', colors.cyan);

    if (IS_WINDOWS) {
      log('MeCab không được hỗ trợ chính thức trên Windows. Sẽ bỏ qua.',
          colors.yellow);
      return true; // Bỏ qua trên Windows
    } else if (IS_MACOS) {
      // Kiểm tra xem Homebrew đã được cài đặt chưa
      try {
        execSync('brew --version', {stdio: 'ignore'});
      } catch (error) {
        log('Đang cài đặt Homebrew...', colors.cyan);
        execSync(
            '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"',
            {stdio: 'inherit'});
      }

      // Cài đặt MeCab
      log('Đang cài đặt MeCab...', colors.cyan);
      execSync('brew install mecab', {stdio: 'inherit'});
    } else if (IS_LINUX) {
      // Cài đặt MeCab trên Linux
      if (fs.existsSync('/etc/debian_version')) {
        // Debian/Ubuntu
        log('Đang cài đặt MeCab trên Debian/Ubuntu...', colors.cyan);
        execSync(
            'sudo apt update && sudo apt install -y mecab libmecab-dev mecab-ipadic-utf8',
            {stdio: 'inherit'});
      } else if (fs.existsSync('/etc/fedora-release')) {
        // Fedora
        log('Đang cài đặt MeCab trên Fedora...', colors.cyan);
        execSync('sudo dnf install -y mecab mecab-devel mecab-ipadic',
            {stdio: 'inherit'});
      } else if (fs.existsSync('/etc/redhat-release')) {
        // CentOS/RHEL
        log('Đang cài đặt MeCab trên CentOS/RHEL...', colors.cyan);
        execSync('sudo yum install -y mecab mecab-devel', {stdio: 'inherit'});
      } else {
        log('Không thể xác định hệ điều hành Linux. Vui lòng cài đặt MeCab thủ công.',
            colors.yellow);
        return false;
      }
    }

    // Kiểm tra lại
    const version = execSync('mecab --version').toString().trim().split(
        '\n')[0];
    log(`✅ Đã cài đặt MeCab: ${version}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ Lỗi khi cài đặt MeCab: ${error.message}`, colors.red);
    log('Sẽ bỏ qua MeCab và tiếp tục...', colors.yellow);
    return true; // Tiếp tục ngay cả khi có lỗi
  }
}

// Hàm chính
async function main() {
  log('🚀 Bắt đầu cài đặt các phụ thuộc hệ thống cho Gen Voice',
      colors.bright + colors.cyan);

  // Kiểm tra và cài đặt ffmpeg
  log('\n📋 Kiểm tra ffmpeg:', colors.bright + colors.blue);
  const ffmpegOk = checkFfmpeg();
  if (!ffmpegOk) {
    log('\n📦 Cài đặt ffmpeg:', colors.bright + colors.blue);
    const ffmpegInstalled = installFfmpeg();
    if (!ffmpegInstalled) {
      log('\n❌ Không thể cài đặt ffmpeg. Vui lòng cài đặt thủ công.',
          colors.bright + colors.red);
      log('  - Windows: https://ffmpeg.org/download.html', colors.yellow);
      log('  - macOS: brew install ffmpeg', colors.yellow);
      log('  - Ubuntu/Debian: sudo apt install ffmpeg', colors.yellow);
    }
  }

  // Kiểm tra và cài đặt MeCab
  log('\n📋 Kiểm tra MeCab:', colors.bright + colors.blue);
  const mecabOk = checkMeCab();
  if (!mecabOk) {
    log('\n📦 Cài đặt MeCab:', colors.bright + colors.blue);
    const mecabInstalled = installMeCab();
    if (!mecabInstalled) {
      log('\n❌ Không thể cài đặt MeCab. Vui lòng cài đặt thủ công nếu cần thiết.',
          colors.bright + colors.yellow);
      log('  - macOS: brew install mecab', colors.yellow);
      log('  - Ubuntu/Debian: sudo apt install mecab libmecab-dev mecab-ipadic-utf8',
          colors.yellow);
      log('  - Fedora: sudo dnf install mecab mecab-devel mecab-ipadic',
          colors.yellow);
    }
  }

  log('\n🎉 Cài đặt các phụ thuộc hệ thống hoàn tất!',
      colors.bright + colors.green);
}

// Chạy hàm chính
main().catch(error => {
  log(`❌ Lỗi không mong đợi: ${error.message}`, colors.red);
  process.exit(1);
});
