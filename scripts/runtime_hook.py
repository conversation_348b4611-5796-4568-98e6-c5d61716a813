import os
import sys

def safe_print(message):
    """
    Hàm print an toàn, xử lý lỗi encoding trên Windows.
    """
    try:
        print(message)
    except UnicodeEncodeError:
        # <PERSON>ếu print thông thường thất bại, thử encode sang utf-8 và decode
        # lại với chế độ 'ignore' để loại bỏ các ký tự không hợp lệ.
        encoded_message = message.encode('utf-8', 'ignore').decode('utf-8', 'ignore')
        print(encoded_message)

def setup_environment():
    """
    Thiết lập biến môi trường library path khi chạy trong gói PyInstaller.
    """
    if not getattr(sys, 'frozen', False):
        return

    bundle_dir = os.path.dirname(sys.executable)
    internal_dir = os.path.join(bundle_dir, '_internal')

    # SỬA ĐỔI: Sử dụng hàm safe_print thay vì print thông thường
    safe_print(f"Runtime Hook: Executable is at {sys.executable}")
    safe_print(f"Runtime Hook: Setting library search path to _internal directory: {internal_dir}")

    if not os.path.exists(internal_dir):
        safe_print(f"Runtime Hook: WARNING - _internal directory not found at {internal_dir}")
        return

    # Xác định biến môi trường cho từng HĐH
    if sys.platform == 'darwin':
        env_var = 'DYLD_LIBRARY_PATH'
    elif sys.platform == 'linux':
        env_var = 'LD_LIBRARY_PATH'
    elif sys.platform == 'win32':
        # Trên Windows, PyInstaller xử lý DLL path thông qua manifest.
        # Nhưng việc thêm vào PATH vẫn là một cách dự phòng tốt.
        env_var = 'PATH'
    else:
        return

    current_path = os.environ.get(env_var, '')
    # Thêm _internal vào đầu danh sách đường dẫn
    new_path_parts = [internal_dir]
    if current_path:
        new_path_parts.append(current_path)
    
    os.environ[env_var] = os.pathsep.join(new_path_parts)

    safe_print(f"Runtime Hook: Updated {env_var} to: {os.environ[env_var]}")


# Chạy hàm thiết lập
setup_environment()
safe_print("Runtime Hook: Environment setup complete.")