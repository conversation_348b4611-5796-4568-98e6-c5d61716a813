{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "outDir": "dist", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@renderer/*": ["src/renderer/*"], "@main/*": ["src/main/*"], "@preload/*": ["src/preload/*"], "@python/*": ["src/python/*"], "@store/*": ["src/store/*"], "@types": ["src/types"], "@types/*": ["src/types/*"], "@utils/*": ["src/utils/*"]}, "skipLibCheck": true, "verbatimModuleSyntax": false, "noEmit": false, "noImplicitAny": false}, "include": ["src/preload/**/*.ts"]}