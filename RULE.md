
- Sử dụng Typescript, không sử dụng Javascript
- <PERSON><PERSON><PERSON> tr<PERSON><PERSON> thư mục đơn gi<PERSON>, phân chia rõ ràng, tr<PERSON><PERSON> các file trùng tên (ví dụ quá nhiều file index.ts)
- <PERSON>ôn triển khai giao diện sau đó xác nhận với nhà phát triển rồi mới tiếp tục xử lý logic
- Đ<PERSON>i với các nhiệ<PERSON> vụ phứ<PERSON> tạp, cần viết thành file tài liệu về kế hoạch thực hiện theo các bư<PERSON>, sau mỗi bước thực hiện thì cập nhật kế hoạch
- Luôn viết docs bằng tiếng việt cho tất cả các hàm
- <PERSON><PERSON> lại log cho tất cả các công việc để dễ dàng debug, kể cả frontend và backend. 
- <PERSON><PERSON><PERSON> cập nhật file `ARCHITECTURE.md` sau khi khi đổi kiến trúc dự án