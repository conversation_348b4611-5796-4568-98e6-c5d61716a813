{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "verbatimModuleSyntax": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "outDir": "dist", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@renderer/*": ["src/renderer/*"], "@main/*": ["src/main/*"], "@preload/*": ["src/preload/*"], "@python/*": ["src/python/*"], "@store/*": ["src/store/*"], "@types": ["src/types"], "@utils/*": ["src/utils/*"]}, "skipLibCheck": true, "noImplicitAny": false, "noUnusedLocals": false}, "include": ["src/main/**/*.ts", "src/main/store/**/*.ts"]}