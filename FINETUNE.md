# Hướng dẫn sử dụng Fine-tune XTTS v2

Tài liệu này hướng dẫn cách sử dụng tính năng fine-tune trong ứng dụng Gen Voice để tạo giọng nói mới từ mô hình XTTS v2.

## 1. Tổ<PERSON> quan

Tính năng fine-tune cho phép bạn tạo ra giọng nói mới dựa trên mô hình XTTS v2 bằng cách sử dụng các file âm thanh của riêng bạn. Qu<PERSON> trình này bao gồm các bước:

1. X<PERSON> lý dữ liệu âm thanh đầu vào
2. Tr<PERSON>ch xuất văn bản từ âm thanh sử dụng Whisper
3. Huấn luyện mô hình XTTS v2 với dữ liệu đã xử lý
4. Kiểm tra mô hình đã huấn luyện

## 2. <PERSON><PERSON><PERSON> cầ<PERSON> hệ thống

### 2.1. <PERSON><PERSON><PERSON> cứng

- **CPU**: <PERSON><PERSON><PERSON> thiể<PERSON> 4 nhân (8 nhân khuyến nghị)
- **RAM**: <PERSON><PERSON><PERSON> thiểu 8GB (16GB khuyến nghị)
- **GPU**: Không bắt buộc, nhưng khuyến nghị sử dụng
  - NVIDIA GPU với CUDA (tăng tốc đáng kể)
  - Apple Silicon (M1/M2/M3) với MPS
- **Dung lượng ổ cứng**: Tối thiểu 20GB trống

### 2.2. Yêu cầu về dữ liệu âm thanh

- **Định dạng**: WAV, MP3, FLAC
- **Độ dài mỗi file**: 3-30 giây (tối ưu nhất là 5-15 giây)
- **Tổng thời lượng**: Tối thiểu 3 giây, khuyến nghị 60 giây trở lên
- **Chất lượng**: Âm thanh rõ ràng, ít nhiễu

## 3. Luồng xử lý dữ liệu

### 3.1. Chuẩn bị dữ liệu

Để có kết quả tốt nhất, bạn nên chuẩn bị dữ liệu theo cách sau:

1. **Thu thập các file âm thanh**:
   - Ghi âm giọng nói của bạn hoặc sử dụng các file âm thanh có sẵn
   - Mỗi file nên chứa một câu hoặc đoạn văn ngắn
   - Đảm bảo âm thanh rõ ràng, không có tiếng ồn nền

2. **Tổ chức thư mục**:
   - Đặt tất cả các file âm thanh vào một thư mục
   - Hỗ trợ các định dạng: WAV, MP3, FLAC
   - Có thể tổ chức thành các thư mục con nếu cần

### 3.2. Quy trình xử lý dữ liệu

Khi bạn chọn thư mục chứa dữ liệu âm thanh, hệ thống sẽ thực hiện các bước sau:

1. **Quét thư mục**:
   - Tìm tất cả các file âm thanh (WAV, MP3, FLAC) trong thư mục và các thư mục con
   - Loại bỏ các đường dẫn trùng lặp

2. **Xử lý từng file âm thanh**:
   - Tải file âm thanh và kiểm tra định dạng
   - Chuyển đổi stereo sang mono nếu cần
   - Tính toán thời lượng của file âm thanh

3. **Trích xuất văn bản**:
   - Sử dụng mô hình Whisper để tự động trích xuất văn bản từ âm thanh
   - Hỗ trợ nhiều ngôn ngữ (tiếng Anh, tiếng Việt, ...)
   - Phân đoạn âm thanh thành các câu dựa trên kết quả của Whisper

4. **Tạo các đoạn âm thanh**:
   - Chia các file âm thanh dài thành các đoạn ngắn hơn dựa trên phân đoạn câu
   - Thêm khoảng đệm (buffer) trước và sau mỗi đoạn
   - Lưu các đoạn âm thanh vào thư mục output/wavs

5. **Tạo metadata**:
   - Tạo file metadata chứa thông tin về các đoạn âm thanh và văn bản tương ứng
   - Thêm thông tin về độ dài (duration) của mỗi đoạn âm thanh
   - Chia dữ liệu thành tập huấn luyện (train) và tập đánh giá (eval)

## 4. Quá trình fine-tune

### 4.1. Các bước fine-tune

Sau khi xử lý dữ liệu, hệ thống sẽ thực hiện quá trình fine-tune:

1. **Chuẩn bị mô hình**:
   - Tải mô hình XTTS v2 gốc
   - Chuẩn bị môi trường huấn luyện (CPU, CUDA hoặc MPS)

2. **Huấn luyện mô hình**:
   - Fine-tune encoder GPT với dữ liệu đã xử lý
   - Huấn luyện qua số epoch đã chỉ định
   - Lưu các checkpoint trong quá trình huấn luyện

3. **Hoàn thiện mô hình**:
   - Chọn checkpoint tốt nhất
   - Tạo file model.pth từ checkpoint đó
   - Lưu các file cấu hình và metadata

4. **Kiểm tra mô hình**:
   - Tạo âm thanh mẫu từ văn bản kiểm tra
   - Lưu kết quả vào thư mục output

### 4.2. Cấu hình mặc định

Hệ thống sử dụng các cấu hình mặc định sau:

- **Mô hình Whisper**: tiny (nhẹ, nhanh nhưng độ chính xác thấp hơn)
- **Độ dài tối đa của âm thanh**: 30 giây
- **Độ dài tối thiểu của âm thanh**: 3 giây
- **Độ dài khuyến nghị**: 60 giây trở lên
- **Tỷ lệ dữ liệu đánh giá**: 15%
- **Khoảng đệm thời gian**: 0.2 giây

## 5. Cấu trúc thư mục kết quả

Sau khi hoàn tất quá trình fine-tune, kết quả sẽ được lưu với cấu trúc thư mục như sau:

```
output_folder/
├── metadata_train.csv       # Dữ liệu huấn luyện
├── metadata_eval.csv        # Dữ liệu kiểm tra
├── wavs/                    # Các file âm thanh đã xử lý
├── voice_name.pth           # Mô hình đã fine-tune
├── voice_name_config.json   # Thông tin cấu hình
└── trained_model/
    └── run/
        └── training/
            └── GPT_XTTS_FT-[DATE]-[HASH]/
                ├── checkpoint_*.pth     # Các checkpoint
                ├── model.pth            # Bản sao của checkpoint tốt nhất
                ├── config.json          # File cấu hình
                ├── vocab.json           # File từ vựng
                └── test_output.wav      # Âm thanh mẫu
```

## 6. Hiển thị thông tin trong giao diện

Trong giao diện người dùng, bạn sẽ thấy:

1. **Danh sách file âm thanh**:
   - Tên file
   - Độ dài của file (tính bằng giây)
   - Nội dung văn bản (được trích xuất hoặc từ file .txt)

2. **Tiến trình xử lý**:
   - Trạng thái hiện tại (đang xử lý, hoàn thành, thất bại)
   - Phần trăm hoàn thành
   - Logs chi tiết về quá trình xử lý

3. **Kết quả fine-tune**:
   - Thông tin về mô hình đã tạo
   - Đường dẫn đến các file mô hình
   - File âm thanh mẫu để kiểm tra

## 7. Khắc phục sự cố

### 7.1. Lỗi về bộ nhớ

Nếu gặp lỗi "CUDA out of memory" hoặc "Memory error":
- Hệ thống sẽ tự động giảm batch_size xuống 2 khi sử dụng CPU
- Có thể giảm số lượng file âm thanh đầu vào
- Đảm bảo đủ dung lượng ổ cứng trống (tối thiểu 20GB)

### 7.2. Lỗi khi tải mô hình

Nếu gặp lỗi khi tải mô hình Whisper hoặc XTTS v2:
- Hệ thống sẽ thử tải mô hình nhỏ hơn (tiny.en) nếu không tải được mô hình đã chọn
- Kiểm tra kết nối internet

### 7.3. Chất lượng âm thanh đầu ra

Nếu chất lượng âm thanh đầu ra không tốt:
- Đảm bảo dữ liệu đầu vào có chất lượng tốt
- Tăng tổng thời lượng âm thanh đầu vào (khuyến nghị 60 giây trở lên)
- Tăng số epoch huấn luyện (15-20 epoch)

## 8. Lưu ý quan trọng

- Quá trình fine-tune có thể mất từ vài phút đến vài giờ tùy thuộc vào cấu hình máy và lượng dữ liệu
- Kết quả tốt nhất đạt được khi sử dụng GPU (NVIDIA hoặc Apple Silicon)
- Đảm bảo âm thanh đầu vào có chất lượng tốt, rõ ràng và không có tiếng ồn
- Mỗi file âm thanh nên có độ dài từ 3-30 giây, với tổng thời lượng tối thiểu 3 giây (khuyến nghị 60 giây trở lên)
- Hệ thống sẽ tự động xử lý và chia nhỏ các file âm thanh dài
