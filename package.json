{"name": "gen-voice", "private": true, "version": "1.0.0", "type": "module", "author": {"name": "<PERSON><PERSON><PERSON><PERSON> Hưởng", "email": "<EMAIL>"}, "scripts": {"setup": "node scripts/setup-dev-env.js", "setup-system-deps": "node scripts/install-system-deps.js", "dev": "concurrently \"vite\" \"NODE_ENV=development node scripts/build-electron.cjs\"", "build": "vite build && node scripts/build-electron.cjs && electron-builder", "build-python": "pyinstaller --onefile --add-data \"src/python:python\" --distpath resources/bundled_python src/python/main.py", "preview": "vite preview", "electron:dev": "npm run setup && npm run check-python-deps && vite --mode development", "electron:universal": "npm run check-python-deps && npm run bundle-python && vite build && node scripts/build-electron.cjs && electron-builder --mac --universal", "electron:preview": "vite build && electron-builder --dir", "vite-electron-builder": "node scripts/build-electron.cjs", "bundle-python": "node scripts/bundle-python.js"}, "main": "dist/index.cjs", "build": {"appId": "com.genvoice.app", "productName": "Gen Voice", "directories": {"output": "release"}, "files": ["dist/**/*", "resources/**/*", "public/**/*"], "extraResources": [{"from": "resources/bundled_python", "to": "bundled_python"}], "mac": {"target": "dmg", "artifactName": "Gen Voice-${version}-${arch}.${ext}", "category": "public.app-category.utilities", "identity": null, "hardenedRuntime": true, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "generateUpdatesFilesForAllChannels": false, "darkModeSupport": true, "icon": "public/logo.icns"}, "win": {"target": "nsis", "icon": "public/logo.ico"}}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@types/archiver": "^6.0.3", "adm-zip": "^0.5.10", "archiver": "^7.0.1", "electron-store": "^10.0.1", "fs-extra": "^11.2.0", "sortablejs": "^1.15.6", "tar": "^6.2.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-i18n": "^10.0.7", "vue-router": "^4.5.1"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.8", "@types/electron-store": "^3.2.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "electron": "^36.0.1", "electron-builder": "^26.0.12", "esbuild": "^0.20.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.5", "typescript": "~5.7.2", "vite": "^6.3.1", "vite-plugin-electron": "^0.29.0", "vue-tsc": "^2.2.8"}}