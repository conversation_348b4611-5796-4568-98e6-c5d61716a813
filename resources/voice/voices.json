{"voices": [{"id": "coqui-ai-xttsv2", "name": "CoquiAI X-TTSv2", "language": ["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh-cn", "ja", "hu", "ko", "hi"], "modelPath": "tts_models/multilingual/multi-dataset/xtts_v2", "referencePaths": ["reference_1.wav", "reference_2.wav", "reference_3.wav", "reference_4.wav", "reference_5.wav"], "previewPath": "CoquiAI X-TTSv2.wav", "description": "I am a multilingual text-to-speech system that clones voices with just a 6-second audio clip, supports 17 languages, and offers improved voice cloning, cross-language capabilities, and high-quality 24kHz audio output.", "tags": ["male", "deep", "rich"], "temperature": 0.4, "repetitionPenalty": 4, "topK": 25, "topP": 0.65, "lengthPenalty": 1, "speed": 1, "type": "builtin"}, {"id": "coqui-ai-your-tts", "name": "CoquiAI Your TTS", "language": ["en"], "modelPath": "tts_models/multilingual/multi-dataset/your_tts", "referencePaths": ["reference_1.wav", "reference_2.wav", "reference_3.wav", "reference_4.wav", "reference_5.wav"], "previewPath": "CoquiAI Your TTS.wav", "description": "I am a Multi-speaker, multi-lingual text-to-speech model that performs voice cloning and zero-shot speaker adaptation using approximately 1 minute of audio, utilizing the VITS architecture with a speaker encoder.", "tags": ["male", "deep", "rich"], "temperature": 0.4, "repetitionPenalty": 4, "topK": 25, "topP": 0.65, "lengthPenalty": 1, "speed": 1, "type": "builtin"}, {"id": "coqui-ai-tacotron2_ddc", "name": "CoquiAI Tacotron2 DDC", "language": ["en"], "modelPath": "tts_models/en/ljspeech/tacotron2-DDC", "referencePaths": ["reference_1.wav", "reference_2.wav", "reference_3.wav", "reference_4.wav", "reference_5.wav"], "previewPath": "CoquiAI Tacotron2 DDC.wav", "description": "I am an Attention-based end-to-end text-to-speech model with Double Decoder Consistency, which improves alignment for long or out-of-domain texts, high-quality speech with flexible speed-quality trade-off.", "tags": ["male", "deep", "rich"], "temperature": 0.4, "repetitionPenalty": 4, "topK": 25, "topP": 0.65, "lengthPenalty": 1, "speed": 1, "type": "builtin"}, {"id": "coqui-ai-vits", "name": "CoquiAI Vits", "language": ["en"], "modelPath": "tts_models/en/ljspeech/vits", "referencePaths": ["reference_1.wav", "reference_2.wav", "reference_3.wav", "reference_4.wav", "reference_5.wav"], "previewPath": "CoquiAI Vits.wav", "description": "I am an End-to-end text-to-speech combining GlowTTS encoder and HiFiGAN vocoder, uses GANs, VAE, and Normalizing Flows, no external alignment needed, fast with high-quality output.", "tags": ["male", "deep", "rich"], "temperature": 0.4, "repetitionPenalty": 4, "topK": 25, "topP": 0.65, "lengthPenalty": 1, "speed": 1, "type": "builtin"}, {"id": "coqui-ai-glow_tts", "name": "CoquiAI Glow TTS", "language": ["en"], "modelPath": "tts_models/en/ljspeech/glow-tts", "referencePaths": ["reference_1.wav", "reference_2.wav", "reference_3.wav", "reference_4.wav", "reference_5.wav"], "previewPath": "CoquiAI Glow TTS.wav", "description": "I am a Flow-based text to speech model, learns text-to-audio alignment without external annotations, fast and efficient with natural-sounding speech.", "tags": ["male", "deep", "rich"], "temperature": 0.4, "repetitionPenalty": 4, "topK": 25, "topP": 0.65, "lengthPenalty": 1, "speed": 1, "type": "builtin"}, {"id": "coqui-ai-speedy_speech", "name": "CoquiAI Speedy Speech", "language": ["en"], "modelPath": "tts_models/en/ljspeech/speedy-speech", "referencePaths": ["reference_1.wav", "reference_2.wav", "reference_3.wav", "reference_4.wav", "reference_5.wav"], "previewPath": "CoquiAI Speedy Speech.wav", "description": "I am an optimized text to speech model for fast speech synthesis, balancing speed and quality, suitable for real-time applications with efficient training.", "tags": ["male", "deep", "rich"], "temperature": 0.4, "repetitionPenalty": 4, "topK": 25, "topP": 0.65, "lengthPenalty": 1, "speed": 1, "type": "builtin"}], "defaultVoice": "coqui-ai-xttsv2"}