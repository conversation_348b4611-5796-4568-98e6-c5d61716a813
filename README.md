# Gen Voice

Ứng dụng desktop cho phép tạo giọng nói tổng hợp từ giọng nói của bạn.

## Cài đặt

### Y<PERSON><PERSON> cầu hệ thống

- Node.js 18+ và npm
- Python 3.9-3.11 (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị Python 3.10.17)
- <PERSON><PERSON><PERSON> phụ thuộc hệ thống: ffmpeg, MeCab (tùy chọn)

### Cài đặt và chạy

1. Clone repository:
   ```bash
   git clone https://github.com/your-username/gen-voice.git
   cd gen-voice
   ```

2. C<PERSON>i đặt các phụ thuộc Node.js:
   ```bash
   npm install
   ```

3. Cài đặt các phụ thuộc hệ thống (ffmpeg, MeCab):
   ```bash
   npm run setup-system-deps
   ```

4. Thiết lập môi trường phát triển (Python, .venv, c<PERSON><PERSON> phụ thuộc <PERSON>):
   ```bash
   npm run setup
   ```

5. <PERSON><PERSON><PERSON> <PERSON>ng dụng trong chế độ phát triển:
   ```bash
   npm run dev
   ```

## Xây dựng ứng dụng

```bash
npm run electron:build
```

## Giải quyết vấn đề

### Lỗi với mecab-python3

Nếu gặp lỗi khi cài đặt mecab-python3, hãy cài đặt MeCab trước:

- macOS: `brew install mecab`
- Ubuntu/Debian: `sudo apt install mecab libmecab-dev mecab-ipadic-utf8`
- Fedora: `sudo dnf install mecab mecab-devel mecab-ipadic`

### Lỗi với ffmpeg

Nếu gặp lỗi với ffmpeg, hãy cài đặt ffmpeg trước:

- macOS: `brew install ffmpeg`
- Ubuntu/Debian: `sudo apt install ffmpeg`
- Windows: Tải từ https://ffmpeg.org/download.html
